from functools import wraps
from typing import Callable, Any
from fastapi import HTTPException, status
from app.schemas.common import ResponseModel
import logging

logger = logging.getLogger(__name__)

def handle_api_exceptions(success_message: str = "操作成功"):
    """统一API异常处理装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            try:
                result = await func(*args, **kwargs)
                if isinstance(result, ResponseModel):
                    return result
                return ResponseModel(
                    success=True,
                    message=success_message,
                    data=result
                )
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"API调用失败 {func.__name__}: {e}", exc_info=True)
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"操作失败: {str(e)}"
                )
        return wrapper
    return decorator