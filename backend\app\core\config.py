"""
系统配置模块
负责管理所有系统配置参数
"""

from typing import Optional, List
from pydantic import BaseSettings, validator
import os
from pathlib import Path

class Settings(BaseSettings):
    """系统配置类"""
    
    # 应用基础配置
    APP_NAME: str = "NL2SQL 数据智能分析系统"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    
    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # 数据库配置
    DATABASE_URL: str = "mysql+pymysql://root:password@localhost:3306/nl2sql_system"
    DATABASE_ECHO: bool = False
    # 新增数据库连接池配置
    DATABASE_POOL_SIZE: int = 20
    DATABASE_MAX_OVERFLOW: int = 30
    DATABASE_POOL_TIMEOUT: int = 30
    DATABASE_POOL_RECYCLE: int = 3600
    
    # Redis配置 (用于缓存和会话)
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # JWT配置
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # 加密配置
    ENCRYPTION_KEY: Optional[str] = None
    
    # CORS配置
    BACKEND_CORS_ORIGINS: List[str] = ["http://localhost:3000", "http://127.0.0.1:3000"]
    
    # 文件上传配置
    UPLOAD_DIR: str = "uploads"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    
    # 查询配置优化
    DEFAULT_QUERY_TIMEOUT: int = 30
    MAX_QUERY_RESULT_ROWS: int = 1000
    ENABLE_QUERY_CACHE: bool = True
    QUERY_CACHE_EXPIRE: int = 3600
    # 新增批量查询配置
    MAX_CONCURRENT_QUERIES: int = 10
    QUERY_RESULT_STREAMING: bool = True
    
    # LLM配置
    DEFAULT_LLM_PROVIDER: str = "openai"
    DEFAULT_LLM_MODEL: str = "gpt-3.5-turbo"
    LLM_REQUEST_TIMEOUT: int = 30
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/app.log"
    
    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v):
        """组装CORS源列表"""
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    @validator("ENCRYPTION_KEY", pre=True)
    def generate_encryption_key(cls, v):
        """生成加密密钥"""
        if not v:
            from cryptography.fernet import Fernet
            return Fernet.generate_key().decode()
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# 创建全局配置实例
settings = Settings()

# 确保必要的目录存在
def ensure_directories():
    """确保必要的目录存在"""
    directories = [
        settings.UPLOAD_DIR,
        "logs",
        "cache"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)

# 初始化目录
ensure_directories()
