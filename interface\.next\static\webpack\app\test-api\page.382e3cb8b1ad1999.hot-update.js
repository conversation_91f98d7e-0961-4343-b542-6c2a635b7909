"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-api/page",{

/***/ "(app-pages-browser)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn() {\n    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){\n        inputs[_key] = arguments[_key];\n    }\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n// API配置\nconst API_BASE_URL = \"http://localhost:8000/api/v1\" || 0;\n// API客户端类\nclass ApiClient {\n    setToken(token) {\n        this.token = token;\n        if (true) {\n            localStorage.setItem('access_token', token);\n        }\n    }\n    clearToken() {\n        this.token = null;\n        if (true) {\n            localStorage.removeItem('access_token');\n        }\n    }\n    async request(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const url = \"\".concat(this.baseURL).concat(endpoint);\n        const headers = {\n            'Content-Type': 'application/json',\n            ...options.headers\n        };\n        if (this.token) {\n            headers.Authorization = \"Bearer \".concat(this.token);\n        }\n        const config = {\n            ...options,\n            headers\n        };\n        try {\n            const response = await fetch(url, config);\n            if (!response.ok) {\n                if (response.status === 401) {\n                    // Token过期，清除token\n                    this.clearToken();\n                    throw new Error('认证失败，请重新登录');\n                }\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            return await response.json();\n        } catch (error) {\n            console.error('API request failed:', error);\n            throw error;\n        }\n    }\n    // GET请求\n    async get(endpoint) {\n        return this.request(endpoint, {\n            method: 'GET'\n        });\n    }\n    // POST请求\n    async post(endpoint, data) {\n        return this.request(endpoint, {\n            method: 'POST',\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n    // PUT请求\n    async put(endpoint, data) {\n        return this.request(endpoint, {\n            method: 'PUT',\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n    // DELETE请求\n    async delete(endpoint) {\n        return this.request(endpoint, {\n            method: 'DELETE'\n        });\n    }\n    // 登录\n    async login(username, password) {\n        const formData = new FormData();\n        formData.append('username', username);\n        formData.append('password', password);\n        const response = await fetch(\"\".concat(this.baseURL, \"/auth/login\"), {\n            method: 'POST',\n            body: formData\n        });\n        if (!response.ok) {\n            throw new Error('登录失败');\n        }\n        const data = await response.json();\n        this.setToken(data.access_token);\n        return data;\n    }\n    // 注册\n    async register(userData) {\n        return this.post('/auth/register', userData);\n    }\n    // 自然语言转SQL并执行\n    async nlToSqlAndExecute(query) {\n        let dataSourceId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n        const response = await this.post('/query/nl2sql-and-execute', {\n            natural_query: query,\n            data_source_id: dataSourceId\n        });\n        // 处理后端返回的嵌套数据结构\n        if (response.success && response.data) {\n            return {\n                success: true,\n                data: response.data.data,\n                columns: response.data.columns,\n                sql: response.data.generated_sql,\n                message: response.message\n            };\n        }\n        return response;\n    }\n    // 执行SQL查询\n    async executeQuery(sql) {\n        let dataSourceId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n        const response = await this.post('/query/execute', {\n            sql,\n            data_source_id: dataSourceId\n        });\n        // 处理后端返回的嵌套数据结构\n        if (response.success && response.data) {\n            return {\n                success: true,\n                data: response.data.data || response.data,\n                columns: response.data.columns,\n                message: response.message\n            };\n        }\n        return response;\n    }\n    // 获取数据源列表\n    async getDataSources() {\n        return this.get('/data-sources/');\n    }\n    // 获取查询历史\n    async getQueryHistory() {\n        return this.get('/query/history');\n    }\n    // 获取收藏查询\n    async getFavoriteQueries() {\n        return this.get('/favorites/');\n    }\n    // 获取系统健康状态\n    async getSystemHealth() {\n        return this.get('/system/health');\n    }\n    constructor(baseURL){\n        this.token = null;\n        this.baseURL = baseURL;\n        // 从localStorage获取token\n        if (true) {\n            this.token = localStorage.getItem('access_token');\n        }\n    }\n}\n// 创建全局API客户端实例\nconst apiClient = new ApiClient(API_BASE_URL);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/utils.ts\n"));

/***/ })

});