2025-07-18 18:26:08 - uvicorn.error - INFO - server.py:76 - serve - Started server process [26016]
2025-07-18 18:26:08 - uvicorn.error - INFO - on.py:46 - startup - Waiting for application startup.
2025-07-18 18:26:08 - main - INFO - main.py:27 - lifespan - 正在启动 NL2SQL 数据智能分析系统...
2025-07-18 18:26:09 - app.db.init_db - INFO - init_db.py:20 - init_db - 数据库表创建完成
2025-07-18 18:26:09 - app.db.init_db - INFO - init_db.py:26 - init_db - 初始数据创建完成
2025-07-18 18:26:09 - main - INFO - main.py:32 - lifespan - 数据库初始化完成
2025-07-18 18:26:09 - main - INFO - main.py:37 - lifespan - 系统启动完成
2025-07-18 18:26:09 - uvicorn.error - INFO - on.py:60 - startup - Application startup complete.
2025-07-18 18:26:31 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:62546 - "OPTIONS /api/v1/data-sources?limit=100 HTTP/1.1" 200
2025-07-18 18:26:31 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:62549 - "OPTIONS /api/v1/query/history?limit=10 HTTP/1.1" 200
2025-07-18 18:26:31 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:62547 - "OPTIONS /api/v1/query/history?limit=10 HTTP/1.1" 200
2025-07-18 18:26:31 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:62548 - "OPTIONS /api/v1/data-sources?limit=100 HTTP/1.1" 200
2025-07-18 18:26:31 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:62546 - "GET /api/v1/data-sources?limit=100 HTTP/1.1" 307
2025-07-18 18:26:31 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:62548 - "GET /api/v1/data-sources?limit=100 HTTP/1.1" 307
2025-07-18 18:26:31 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:62546 - "OPTIONS /api/v1/data-sources/?limit=100 HTTP/1.1" 200
2025-07-18 18:26:31 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:62547 - "GET /api/v1/query/history?limit=10 HTTP/1.1" 401
2025-07-18 18:26:31 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:62546 - "OPTIONS /api/v1/data-sources/?limit=100 HTTP/1.1" 200
2025-07-18 18:26:31 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:62548 - "GET /api/v1/data-sources/?limit=100 HTTP/1.1" 401
2025-07-18 18:26:31 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:62547 - "GET /api/v1/query/history?limit=10 HTTP/1.1" 401
2025-07-18 18:26:31 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:62547 - "GET /api/v1/data-sources/?limit=100 HTTP/1.1" 401
2025-07-21 09:50:47 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:53818 - "OPTIONS /api/v1/query/history?limit=10 HTTP/1.1" 200
2025-07-21 09:50:47 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:53819 - "OPTIONS /api/v1/data-sources?limit=100 HTTP/1.1" 200
2025-07-21 09:50:47 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:53820 - "OPTIONS /api/v1/query/history?limit=10 HTTP/1.1" 200
2025-07-21 09:50:47 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:53817 - "OPTIONS /api/v1/data-sources?limit=100 HTTP/1.1" 200
2025-07-21 09:50:47 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:53820 - "GET /api/v1/data-sources?limit=100 HTTP/1.1" 307
2025-07-21 09:50:47 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:53820 - "OPTIONS /api/v1/data-sources/?limit=100 HTTP/1.1" 200
2025-07-21 09:50:47 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:53817 - "GET /api/v1/data-sources?limit=100 HTTP/1.1" 307
2025-07-21 09:50:47 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:53818 - "GET /api/v1/query/history?limit=10 HTTP/1.1" 401
2025-07-21 09:50:47 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:53820 - "GET /api/v1/data-sources/?limit=100 HTTP/1.1" 401
2025-07-21 09:50:47 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:53818 - "GET /api/v1/query/history?limit=10 HTTP/1.1" 401
2025-07-21 09:50:47 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:53820 - "GET /api/v1/data-sources/?limit=100 HTTP/1.1" 401
2025-07-21 09:52:54 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:54130 - "GET /api/v1/data-sources?limit=100 HTTP/1.1" 307
2025-07-21 09:52:54 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:54131 - "GET /api/v1/query/history?limit=10 HTTP/1.1" 401
2025-07-21 09:52:54 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:54130 - "GET /api/v1/data-sources/?limit=100 HTTP/1.1" 401
2025-07-21 09:53:11 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:54132 - "GET /api/v1/data-sources?limit=100 HTTP/1.1" 307
2025-07-21 09:53:11 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:54132 - "GET /api/v1/query/history?limit=10 HTTP/1.1" 401
2025-07-21 09:53:11 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:54132 - "GET /api/v1/data-sources/?limit=100 HTTP/1.1" 401
2025-07-21 09:55:43 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:54478 - "GET /api/v1/data-sources?limit=100 HTTP/1.1" 307
2025-07-21 09:55:43 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:54479 - "GET /api/v1/query/history?limit=10 HTTP/1.1" 401
2025-07-21 09:55:43 - uvicorn.access - INFO - h11_impl.py:478 - send - 127.0.0.1:54479 - "GET /api/v1/data-sources/?limit=100 HTTP/1.1" 401
2025-07-21 10:54:05 - uvicorn.error - INFO - server.py:264 - shutdown - Shutting down
2025-07-21 10:54:06 - uvicorn.error - INFO - on.py:65 - shutdown - Waiting for application shutdown.
2025-07-21 10:54:06 - main - INFO - main.py:41 - lifespan - 正在关闭系统...
2025-07-21 10:54:06 - uvicorn.error - INFO - on.py:76 - shutdown - Application shutdown complete.
2025-07-21 10:54:06 - uvicorn.error - INFO - server.py:86 - serve - Finished server process [26016]
2025-07-21 10:54:10 - uvicorn.error - INFO - server.py:76 - serve - Started server process [38484]
2025-07-21 10:54:10 - uvicorn.error - INFO - on.py:46 - startup - Waiting for application startup.
2025-07-21 10:54:10 - main - INFO - main.py:27 - lifespan - 正在启动 NL2SQL 数据智能分析系统...
2025-07-21 10:54:11 - app.db.init_db - INFO - init_db.py:20 - init_db - 数据库表创建完成
2025-07-21 10:54:11 - app.db.init_db - INFO - init_db.py:26 - init_db - 初始数据创建完成
2025-07-21 10:54:11 - main - INFO - main.py:32 - lifespan - 数据库初始化完成
2025-07-21 10:54:11 - main - INFO - main.py:37 - lifespan - 系统启动完成
2025-07-21 10:54:11 - uvicorn.error - INFO - on.py:60 - startup - Application startup complete.
2025-07-21 11:42:42 - uvicorn.error - INFO - server.py:264 - shutdown - Shutting down
2025-07-21 11:42:42 - uvicorn.error - INFO - on.py:65 - shutdown - Waiting for application shutdown.
2025-07-21 11:42:42 - main - INFO - main.py:41 - lifespan - 正在关闭系统...
2025-07-21 11:42:42 - uvicorn.error - INFO - on.py:76 - shutdown - Application shutdown complete.
2025-07-21 11:42:42 - uvicorn.error - INFO - server.py:86 - serve - Finished server process [38484]
2025-07-21 11:42:51 - uvicorn.error - INFO - server.py:76 - serve - Started server process [30528]
2025-07-21 11:42:51 - uvicorn.error - INFO - on.py:46 - startup - Waiting for application startup.
2025-07-21 11:42:51 - main - INFO - main.py:28 - lifespan - 正在启动 NL2SQL 数据智能分析系统...
2025-07-21 11:42:51 - app.db.init_db - INFO - init_db.py:20 - init_db - 数据库表创建完成
2025-07-21 11:42:51 - app.db.init_db - INFO - init_db.py:26 - init_db - 初始数据创建完成
2025-07-21 11:42:51 - main - INFO - main.py:33 - lifespan - 数据库初始化完成
2025-07-21 11:42:51 - main - INFO - main.py:38 - lifespan - 系统启动完成
2025-07-21 11:42:51 - uvicorn.error - INFO - on.py:60 - startup - Application startup complete.
