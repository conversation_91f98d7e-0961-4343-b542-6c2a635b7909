"""
查询相关API端点
包括NL2SQL转换、查询执行、历史记录等
"""

from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.schemas.query import (
    NL2SQLRequest, NL2SQLResponse, QueryExecuteRequest, QueryExecuteResponse,
    QueryHistoryResponse, QueryExportRequest
)
from app.schemas.common import ResponseModel, PaginatedResponse
from app.core.decorators import handle_api_exceptions
from app.services.nl2sql_service import NL2SQLService
from app.services.query_service import QueryService

router = APIRouter()

@router.post("/nl2sql", response_model=ResponseModel[NL2SQLResponse])
@handle_api_exceptions("自然语言转SQL成功")
async def convert_nl_to_sql(
    request: NL2SQLRequest,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
) -> NL2SQLResponse:
    """自然语言转SQL"""
    nl2sql_service = NL2SQLService(db)
    result = await nl2sql_service.convert_to_sql(
        natural_query=request.natural_query,
        data_source_id=request.data_source_id,
        user_id=current_user.id,
        context=request.context
    )
    return result

@router.post("/execute", response_model=ResponseModel[QueryExecuteResponse])
@handle_api_exceptions("查询执行成功")
async def execute_query(
    request: QueryExecuteRequest,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
) -> QueryExecuteResponse:
    """执行SQL查询"""
    query_service = QueryService(db)
    result = await query_service.execute_query(
        sql=request.sql,
        data_source_id=request.data_source_id,
        user_id=current_user.id,
        natural_language_query=request.natural_language_query,
        use_cache=request.use_cache
    )
    return result

@router.post("/nl2sql-and-execute", response_model=ResponseModel[QueryExecuteResponse])
async def nl2sql_and_execute(
    request: NL2SQLRequest,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
) -> Any:
    """自然语言转SQL并执行"""
    try:
        # 临时实现 - 返回示例数据
        result = {
            "success": True,
            "data": [
                {"id": 1, "username": "user1", "email": "<EMAIL>", "created_at": "2023-01-15"},
                {"id": 2, "username": "user2", "email": "<EMAIL>", "created_at": "2023-02-20"},
                {"id": 3, "username": "user3", "email": "<EMAIL>", "created_at": "2023-03-10"}
            ],
            "columns": ["id", "username", "email", "created_at"],
            "row_count": 3,
            "execution_time": 0.045,
            "generated_sql": "SELECT * FROM users WHERE created_at >= '2023-01-01' LIMIT 5;",
            "llm_provider": "openai",
            "model": "gpt-3.5-turbo"
        }
        
        return ResponseModel(
            success=True,
            message="自然语言查询执行成功",
            data=result
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"查询失败: {str(e)}"
        )

@router.get("/history", response_model=PaginatedResponse[QueryHistoryResponse])
def get_query_history(
    skip: int = 0,
    limit: int = 50,
    data_source_id: Optional[int] = None,
    status: Optional[str] = None,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
) -> Any:
    """获取查询历史"""
    try:
        # 临时实现 - 返回示例历史记录
        history_items = [
            {
                "id": 1,
                "natural_language_query": "查询2023年销售额最高的5个产品",
                "sql_query": "SELECT product_name, SUM(sales_amount) as total_sales FROM sales WHERE year = 2023 GROUP BY product_name ORDER BY total_sales DESC LIMIT 5;",
                "status": "success",
                "execution_time": 0.123,
                "row_count": 5,
                "error_message": None,
                "data_source_id": 1,
                "created_at": "2024-01-15T10:30:00Z"
            },
            {
                "id": 2,
                "natural_language_query": "统计每个月的用户注册数量",
                "sql_query": "SELECT DATE_FORMAT(created_at, '%Y-%m') as month, COUNT(*) as user_count FROM users GROUP BY month ORDER BY month;",
                "status": "success",
                "execution_time": 0.089,
                "row_count": 12,
                "error_message": None,
                "data_source_id": 1,
                "created_at": "2024-01-15T09:15:00Z"
            }
        ]
        
        return PaginatedResponse(
            success=True,
            message="获取查询历史成功",
            data=history_items,
            total=len(history_items),
            page=skip // limit + 1,
            page_size=limit
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"获取查询历史失败: {str(e)}"
        )

@router.post("/export", response_model=ResponseModel)
async def export_query_results(
    request: QueryExportRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
) -> Any:
    """导出查询结果"""
    try:
        # 临时实现
        return ResponseModel(
            success=True,
            message="导出任务已启动，请稍后查看结果"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"导出失败: {str(e)}"
        )

@router.get("/validate-sql", response_model=ResponseModel)
async def validate_sql(
    sql: str,
    data_source_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
) -> Any:
    """验证SQL语法"""
    try:
        # 临时实现 - 简单验证
        if not sql.strip():
            raise ValueError("SQL不能为空")
        
        return ResponseModel(
            success=True,
            message="SQL验证通过",
            data={
                "original_sql": sql,
                "validated_sql": sql,
                "is_valid": True
            }
        )
        
    except Exception as e:
        return ResponseModel(
            success=False,
            message=f"SQL验证失败: {str(e)}",
            data={
                "original_sql": sql,
                "validated_sql": None,
                "is_valid": False,
                "error": str(e)
            }
        )

