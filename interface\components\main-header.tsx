"use client"

import Link from "next/link"
import { useState, useEffect } from "react" // Explicitly import React
import { useRouter } from "next/navigation"
import { HelpCircle, User, Settings, LogOut, FileText } from "lucide-react" // Removed Moon, Sun
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
// Removed useTheme, Switch, Label imports

export default function MainHeader() {
  const router = useRouter()
  const [isAdmin, setIsAdmin] = useState(true) // Mock admin status for header
  // Removed theme and setTheme states
  const [mounted, setMounted] = useState(false) // New mounted state

  // useEffect only runs on the client, so now we can safely show the UI
  useEffect(() => {
    setMounted(true)
  }, [])

  const handleLogout = () => {
    // In a real application, you would clear user session/token here
    console.log("Logging out...")
    router.push("/login") // Redirect to login page
  }

  return (
    <div className="flex items-center justify-between px-4 py-2 border-b border-gray-200 bg-white fixed top-0 left-0 right-0 z-50">
      <div className="flex items-center">
        <Link href="/" className="text-lg font-semibold text-gray-800 cursor-pointer">
          <h1>NL2SQL 数据智能分析系统</h1>
        </Link>
      </div>
      <div className="flex items-center space-x-2">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="ghost" size="icon" asChild>
                <Link href="/">
                  <FileText className="h-5 w-5" />
                </Link>
              </Button>
            </TooltipTrigger>
            <TooltipContent>新建查询</TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              {/* Reverting Link usage to use asChild on Button */}
              <Button variant="ghost" size="icon" asChild>
                <Link href="/docs">
                  <HelpCircle className="h-5 w-5" />
                </Link>
              </Button>
            </TooltipTrigger>
            <TooltipContent>帮助文档</TooltipContent>
          </Tooltip>
        </TooltipProvider>

        {/* Theme Toggle Switch - Removed */}

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon">
              <User className="h-5 w-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem asChild>
              <Link href="/profile">
                <User className="mr-2 h-4 w-4" />
                <span>个人中心</span>
              </Link>
            </DropdownMenuItem>
            {/* Conditionally render based on isAdmin for now */}
            {isAdmin && (
              <DropdownMenuItem asChild>
                <Link href="/settings">
                  <Settings className="mr-2 h-4 w-4" />
                  <span>应用设置</span>
                </Link>
              </DropdownMenuItem>
            )}
            {isAdmin && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/admin-settings">
                    <Settings className="mr-2 h-4 w-4" />
                    <span>系统管理</span>
                  </Link>
                </DropdownMenuItem>
              </>
            )}
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleLogout}>
              <LogOut className="mr-2 h-4 w-4" />
              <span>退出登录</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  )
}
