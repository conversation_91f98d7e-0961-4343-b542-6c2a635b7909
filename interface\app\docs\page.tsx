"use client"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Lightbulb, BookText } from "lucide-react"

// Usage tips
const usageTips = [
  "使用自然语言描述您的分析需求，无需了解SQL语法。",
  "可以指定时间范围，如“过去30天”、“上个季度”、“2023年”等。",
  "可以使用比较词，如“最高的”、“超过平均值的”、“同比增长”等。",
  "可以指定结果数量，如“前10名”、“最低的5个”等。",
  "可以使用专业术语，系统能理解“客单价”、“转化率”、“同比增长”等概念。",
  "尝试使用不同的措辞来表达相同的查询，以获得更准确的结果。",
  "如果结果不符合预期，请尝试更具体或更概括的描述。",
  "系统支持多轮对话，您可以基于之前的查询进行追问。",
]

// Mock documentation content
const documentationContent = [
  {
    title: "系统概述",
    content:
      "NL2SQL 数据智能分析系统旨在通过自然语言处理技术，将用户的日常语言查询自动转换为SQL语句，并从数据仓库中提取和展示数据。这使得非技术用户也能轻松进行数据分析，无需掌握复杂的数据库知识。",
  },
  {
    title: "如何进行查询",
    content:
      "在主页的输入框中，您可以直接输入您的数据分析需求。例如：'查询2023年销售额最高的5个产品'。系统会尝试理解您的意图并生成相应的SQL查询和结果。",
  },
  {
    title: "结果展示与交互",
    content:
      "查询结果将以表格或图表的形式展示。您可以切换不同的视图（表格、柱状图、折线图、饼图）来更好地理解数据。同时，您可以对查询结果进行下载、收藏或反馈。",
  },
  {
    title: "数据元数据浏览",
    content:
      "左侧边栏的'元数据'部分展示了数据仓库的结构，包括数据源、数据库、表和列的详细信息。这有助于您了解可用的数据，并更精确地构建查询。",
  },
  {
    title: "历史查询与收藏",
    content: "在左侧边栏的'历史查询'和'收藏夹'中，您可以查看和管理您之前的查询记录，方便快速回顾和复用。",
  },
]

export default function DocsPage() {
  return (
    <div className="flex flex-col items-center bg-gray-50 p-4">
      <div className="w-full max-w-4xl">
        <h2 className="text-3xl font-bold text-center mb-8 text-gray-800 mt-8">使用文档与技巧</h2>

        <div className="space-y-6">
          {/* Usage Tips Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Lightbulb className="h-6 w-6 mr-2 text-yellow-500" />
                使用技巧
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {usageTips.map((tip, index) => (
                  <li key={index} className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center mr-2 mt-0.5">
                      {index + 1}
                    </div>
                    <p>{tip}</p>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          {/* Documentation Content Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BookText className="h-6 w-6 mr-2 text-blue-500" />
                详细文档
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {documentationContent.map((section, index) => (
                <div key={index}>
                  <h3 className="text-xl font-semibold mb-2">{section.title}</h3>
                  <p className="text-gray-700 leading-relaxed">{section.content}</p>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
