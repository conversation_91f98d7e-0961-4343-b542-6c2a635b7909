"use client"

import { useState } from "react"
import { apiClient } from "@/lib/utils"

export default function TestApiPage() {
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string>("")

  const testHealthCheck = async () => {
    setLoading(true)
    setError("")
    try {
      const response = await fetch('http://localhost:8000/health')
      const data = await response.json()
      setResult(data)
    } catch (err: any) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const testLogin = async () => {
    setLoading(true)
    setError("")
    try {
      const response = await apiClient.login('testuser', 'testpass')
      setResult(response)
    } catch (err: any) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const testQuery = async () => {
    setLoading(true)
    setError("")
    try {
      const response = await apiClient.nlToSqlAndExecute('查询所有用户信息')
      setResult(response)
    } catch (err: any) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">API连接测试</h1>

      <div className="bg-blue-50 p-4 rounded mb-6">
        <h2 className="font-bold mb-2">连接状态:</h2>
        <p>前端: <span className="text-green-600">✓ 运行中 (localhost:3000)</span></p>
        <p>后端: <span className="text-green-600">✓ 运行中 (localhost:8000)</span></p>
        <p>API基础URL: <code className="bg-gray-200 px-2 py-1 rounded">{process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1'}</code></p>
      </div>

      <div className="space-y-4 mb-6">
        <button
          onClick={testHealthCheck}
          disabled={loading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          测试健康检查
        </button>

        <button
          onClick={testLogin}
          disabled={loading}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50 ml-2"
        >
          测试登录
        </button>

        <button
          onClick={testQuery}
          disabled={loading}
          className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50 ml-2"
        >
          测试查询
        </button>
      </div>

      {loading && <div className="text-blue-500">加载中...</div>}
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          错误: {error}
        </div>
      )}
      
      {result && (
        <div className="bg-gray-100 p-4 rounded">
          <h3 className="font-bold mb-2">API响应:</h3>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  )
}
