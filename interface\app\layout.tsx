import type React from "react" // Explicitly import React
import type { Metadata } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "@/components/ui/toaster"
import MainHeader from "@/components/main-header"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "NL2SQL 数据智能分析系统",
  description: "自然语言转SQL的数据智能分析平台",
  generator: "v0.dev",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
          <MainHeader /> {/* Render the main header here */}
          <div className="pt-[56px] h-screen flex flex-col">
            {" "}
            {/* Add padding-top to account for fixed header */}
            {children}
          </div>
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  )
}
