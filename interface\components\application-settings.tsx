"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Settings, Database, Shield, Bell, Palette, Save, RefreshCw } from "lucide-react"

export default function ApplicationSettings() {
  const [settings, setSettings] = useState({
    // General Settings
    appName: "NL2SQL 数据智能分析系统",
    appDescription: "自然语言转SQL的数据智能分析平台",
    defaultLanguage: "zh-CN",
    timezone: "Asia/Shanghai",

    // Query Settings
    maxQueryTimeout: 30,
    maxResultRows: 1000,
    enableQueryCache: true,
    cacheExpiration: 3600,

    // Security Settings
    enableAuditLog: true,
    sessionTimeout: 1800,
    maxLoginAttempts: 5,
    enableTwoFactor: false,

    // Notification Settings
    enableEmailNotifications: true,
    enableSystemAlerts: true,
    notificationEmail: "<EMAIL>",

    // UI Settings
    defaultTheme: "system",
    enableDarkMode: true,
    compactMode: false,
    showWelcomeMessage: true,
  })

  const [isSaving, setIsSaving] = useState(false)

  const handleSave = async () => {
    setIsSaving(true)
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))
    setIsSaving(false)
    // Show success message
  }

  const handleReset = () => {
    // Reset to default values
    setSettings({
      appName: "NL2SQL 数据智能分析系统",
      appDescription: "自然语言转SQL的数据智能分析平台",
      defaultLanguage: "zh-CN",
      timezone: "Asia/Shanghai",
      maxQueryTimeout: 30,
      maxResultRows: 1000,
      enableQueryCache: true,
      cacheExpiration: 3600,
      enableAuditLog: true,
      sessionTimeout: 1800,
      maxLoginAttempts: 5,
      enableTwoFactor: false,
      enableEmailNotifications: true,
      enableSystemAlerts: true,
      notificationEmail: "<EMAIL>",
      defaultTheme: "system",
      enableDarkMode: true,
      compactMode: false,
      showWelcomeMessage: true,
    })
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">应用设置</h1>
          <p className="text-gray-600">管理系统的基本配置和功能设置</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={handleReset}>
            <RefreshCw className="h-4 w-4 mr-2" />
            重置
          </Button>
          <Button onClick={handleSave} disabled={isSaving}>
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? "保存中..." : "保存设置"}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="general" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="general">常规</TabsTrigger>
          <TabsTrigger value="query">查询</TabsTrigger>
          <TabsTrigger value="security">安全</TabsTrigger>
          <TabsTrigger value="notifications">通知</TabsTrigger>
          <TabsTrigger value="appearance">外观</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                基本信息
              </CardTitle>
              <CardDescription>配置应用的基本信息和区域设置</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="appName">应用名称</Label>
                <Input
                  id="appName"
                  value={settings.appName}
                  onChange={(e) => setSettings({ ...settings, appName: e.target.value })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="appDescription">应用描述</Label>
                <Input
                  id="appDescription"
                  value={settings.appDescription}
                  onChange={(e) => setSettings({ ...settings, appDescription: e.target.value })}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="language">默认语言</Label>
                  <Select
                    value={settings.defaultLanguage}
                    onValueChange={(value) => setSettings({ ...settings, defaultLanguage: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="zh-CN">简体中文</SelectItem>
                      <SelectItem value="en-US">English</SelectItem>
                      <SelectItem value="ja-JP">日本語</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="timezone">时区</Label>
                  <Select
                    value={settings.timezone}
                    onValueChange={(value) => setSettings({ ...settings, timezone: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Asia/Shanghai">Asia/Shanghai</SelectItem>
                      <SelectItem value="UTC">UTC</SelectItem>
                      <SelectItem value="America/New_York">America/New_York</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="query" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                查询配置
              </CardTitle>
              <CardDescription>配置SQL查询的执行参数和缓存设置</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="queryTimeout">查询超时时间 (秒)</Label>
                  <Input
                    id="queryTimeout"
                    type="number"
                    value={settings.maxQueryTimeout}
                    onChange={(e) => setSettings({ ...settings, maxQueryTimeout: Number.parseInt(e.target.value) })}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxRows">最大结果行数</Label>
                  <Input
                    id="maxRows"
                    type="number"
                    value={settings.maxResultRows}
                    onChange={(e) => setSettings({ ...settings, maxResultRows: Number.parseInt(e.target.value) })}
                  />
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>启用查询缓存</Label>
                    <p className="text-sm text-gray-600">缓存查询结果以提高性能</p>
                  </div>
                  <Switch
                    checked={settings.enableQueryCache}
                    onCheckedChange={(checked) => setSettings({ ...settings, enableQueryCache: checked })}
                  />
                </div>

                {settings.enableQueryCache && (
                  <div className="space-y-2">
                    <Label htmlFor="cacheExpiration">缓存过期时间 (秒)</Label>
                    <Input
                      id="cacheExpiration"
                      type="number"
                      value={settings.cacheExpiration}
                      onChange={(e) => setSettings({ ...settings, cacheExpiration: Number.parseInt(e.target.value) })}
                    />
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                安全设置
              </CardTitle>
              <CardDescription>配置系统的安全策略和访问控制</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="sessionTimeout">会话超时时间 (秒)</Label>
                  <Input
                    id="sessionTimeout"
                    type="number"
                    value={settings.sessionTimeout}
                    onChange={(e) => setSettings({ ...settings, sessionTimeout: Number.parseInt(e.target.value) })}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxAttempts">最大登录尝试次数</Label>
                  <Input
                    id="maxAttempts"
                    type="number"
                    value={settings.maxLoginAttempts}
                    onChange={(e) => setSettings({ ...settings, maxLoginAttempts: Number.parseInt(e.target.value) })}
                  />
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>启用审计日志</Label>
                    <p className="text-sm text-gray-600">记录用户操作和系统事件</p>
                  </div>
                  <Switch
                    checked={settings.enableAuditLog}
                    onCheckedChange={(checked) => setSettings({ ...settings, enableAuditLog: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>启用双因素认证</Label>
                    <p className="text-sm text-gray-600">为用户账户添加额外的安全层</p>
                  </div>
                  <Switch
                    checked={settings.enableTwoFactor}
                    onCheckedChange={(checked) => setSettings({ ...settings, enableTwoFactor: checked })}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                通知设置
              </CardTitle>
              <CardDescription>配置系统通知和邮件提醒</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="notificationEmail">通知邮箱</Label>
                <Input
                  id="notificationEmail"
                  type="email"
                  value={settings.notificationEmail}
                  onChange={(e) => setSettings({ ...settings, notificationEmail: e.target.value })}
                />
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>启用邮件通知</Label>
                    <p className="text-sm text-gray-600">发送重要事件的邮件通知</p>
                  </div>
                  <Switch
                    checked={settings.enableEmailNotifications}
                    onCheckedChange={(checked) => setSettings({ ...settings, enableEmailNotifications: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>启用系统警报</Label>
                    <p className="text-sm text-gray-600">显示系统状态和错误警报</p>
                  </div>
                  <Switch
                    checked={settings.enableSystemAlerts}
                    onCheckedChange={(checked) => setSettings({ ...settings, enableSystemAlerts: checked })}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="appearance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="h-5 w-5" />
                外观设置
              </CardTitle>
              <CardDescription>自定义界面外观和用户体验</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="theme">默认主题</Label>
                <Select
                  value={settings.defaultTheme}
                  onValueChange={(value) => setSettings({ ...settings, defaultTheme: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="light">浅色</SelectItem>
                    <SelectItem value="dark">深色</SelectItem>
                    <SelectItem value="system">跟随系统</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Separator />

              <div className="space-y-4">
                

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>紧凑模式</Label>
                    <p className="text-sm text-gray-600">使用更紧凑的界面布局</p>
                  </div>
                  <Switch
                    checked={settings.compactMode}
                    onCheckedChange={(checked) => setSettings({ ...settings, compactMode: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>显示欢迎消息</Label>
                    <p className="text-sm text-gray-600">在首页显示欢迎和帮助信息</p>
                  </div>
                  <Switch
                    checked={settings.showWelcomeMessage}
                    onCheckedChange={(checked) => setSettings({ ...settings, showWelcomeMessage: checked })}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
