from enum import Enum

class ErrorCode(str, Enum):
    """统一错误码定义"""
    # 通用错误 1000-1999
    INTERNAL_ERROR = "1000"
    INVALID_PARAMETER = "1001"
    PERMISSION_DENIED = "1002"
    
    # 认证错误 2000-2999
    AUTH_TOKEN_INVALID = "2000"
    AUTH_TOKEN_EXPIRED = "2001"
    AUTH_LOGIN_FAILED = "2002"
    
    # 数据源错误 3000-3999
    DATA_SOURCE_NOT_FOUND = "3000"
    DATA_SOURCE_CONNECTION_FAILED = "3001"
    DATA_SOURCE_PERMISSION_DENIED = "3002"
    
    # 查询错误 4000-4999
    QUERY_SYNTAX_ERROR = "4000"
    QUERY_TIMEOUT = "4001"
    QUERY_RESULT_TOO_LARGE = "4002"
    
    # NL2SQL错误 5000-5999
    NL2SQL_CONVERSION_FAILED = "5000"
    NL2SQL_UNSUPPORTED_QUERY = "5001"
    NL2SQL_LLM_ERROR = "5002"

ERROR_MESSAGES = {
    ErrorCode.INTERNAL_ERROR: "服务器内部错误",
    ErrorCode.INVALID_PARAMETER: "参数无效",
    ErrorCode.DATA_SOURCE_NOT_FOUND: "数据源不存在",
    ErrorCode.QUERY_SYNTAX_ERROR: "SQL语法错误",
    ErrorCode.NL2SQL_CONVERSION_FAILED: "自然语言转换失败",
    # ... 其他错误消息
}