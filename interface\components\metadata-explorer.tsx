"use client"

import { useState } from "react"
import { ChevronRight, ChevronDown, Database, Table, FileText, Server } from "lucide-react" // Added Server icon
import { <PERSON>ltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

// Define the new structure for columns, tables, databases, and data sources
interface Column {
  id: string
  name: string
  type: string
  description: string
}

interface TableNode {
  id: string
  name: string
  description: string
  columns: Column[]
}

interface DatabaseNode {
  id: string
  name: string
  description: string
  tables: TableNode[]
}

interface DataSourceNode {
  id: string
  name: string
  description: string
  databases: DatabaseNode[]
}

// Mock metadata structure with Data Source level
const mockMetadata: DataSourceNode[] = [
  {
    id: "ds1",
    name: "数据源A",
    description: "第一个数据源，包含销售和客户数据",
    databases: [
      {
        id: "db1",
        name: "销售数据库",
        description: "包含所有销售相关的数据表",
        tables: [
          {
            id: "table1",
            name: "sales_data",
            description: "销售交易明细表",
            columns: [
              { id: "col1", name: "transaction_id", type: "VARCHAR", description: "交易ID" },
              { id: "col2", name: "product_id", type: "VARCHAR", description: "产品ID" },
              { id: "col3", name: "product_name", type: "VARCHAR", description: "产品名称" },
              { id: "col4", name: "sales_amount", type: "DECIMAL", description: "销售金额" },
              { id: "col5", name: "sales_date", type: "DATE", description: "销售日期" },
              { id: "col6", name: "customer_id", type: "VARCHAR", description: "客户ID" },
            ],
          },
          {
            id: "table2",
            name: "products",
            description: "产品信息表",
            columns: [
              { id: "col7", name: "product_id", type: "VARCHAR", description: "产品ID" },
              { id: "col8", name: "product_name", type: "VARCHAR", description: "产品名称" },
              { id: "col9", name: "category", type: "VARCHAR", description: "产品类别" },
              { id: "col10", name: "price", type: "DECIMAL", description: "产品价格" },
            ],
          },
        ],
      },
      {
        id: "db2",
        name: "客户数据库",
        description: "包含所有客户相关的数据表",
        tables: [
          {
            id: "table3",
            name: "customers",
            description: "客户信息表",
            columns: [
              { id: "col11", name: "customer_id", type: "VARCHAR", description: "客户ID" },
              { id: "col12", name: "customer_name", type: "VARCHAR", description: "客户名称" },
              { id: "col13", name: "customer_type", type: "VARCHAR", description: "客户类型" },
              { id: "col14", name: "region", type: "VARCHAR", description: "所在地区" },
            ],
          },
        ],
      },
    ],
  },
  {
    id: "ds2",
    name: "数据源B",
    description: "第二个数据源，包含订单数据",
    databases: [
      {
        id: "db3",
        name: "订单数据库",
        description: "包含订单信息",
        tables: [
          {
            id: "table4",
            name: "orders",
            description: "订单主表",
            columns: [
              { id: "col15", name: "order_id", type: "VARCHAR", description: "订单ID" },
              { id: "col16", name: "order_date", type: "DATE", description: "订单日期" },
            ],
          },
        ],
      },
    ],
  },
]

type ExpandedState = {
  [key: string]: boolean
}

export default function MetadataExplorer() {
  const [expanded, setExpanded] = useState<ExpandedState>({})

  const toggleExpand = (id: string) => {
    setExpanded((prev) => ({
      ...prev,
      [id]: !prev[id],
    }))
  }

  return (
    <div className="p-2">
      <div className="text-sm font-medium text-gray-500 mb-2 px-2">数据仓库元数据</div>
      <div className="space-y-1">
        {mockMetadata.map((dataSource) => (
          <div key={dataSource.id} className="text-sm">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    onClick={() => toggleExpand(dataSource.id)}
                    className="flex items-center w-full p-2 hover:bg-gray-100 rounded-md text-left"
                  >
                    {expanded[dataSource.id] ? (
                      <ChevronDown className="h-4 w-4 mr-1" />
                    ) : (
                      <ChevronRight className="h-4 w-4 mr-1" />
                    )}
                    <Server className="h-4 w-4 mr-2 text-purple-600" /> {/* Data Source Icon */}
                    <span>{dataSource.name}</span>
                  </button>
                </TooltipTrigger>
                <TooltipContent side="right">{dataSource.description}</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {expanded[dataSource.id] && (
              <div className="ml-6 space-y-1 mt-1">
                {dataSource.databases.map((database) => (
                  <div key={database.id}>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <button
                            onClick={() => toggleExpand(database.id)}
                            className="flex items-center w-full p-2 hover:bg-gray-100 rounded-md text-left"
                          >
                            {expanded[database.id] ? (
                              <ChevronDown className="h-4 w-4 mr-1" />
                            ) : (
                              <ChevronRight className="h-4 w-4 mr-1" />
                            )}
                            <Database className="h-4 w-4 mr-2 text-blue-600" /> {/* Database Icon */}
                            <span>{database.name}</span>
                          </button>
                        </TooltipTrigger>
                        <TooltipContent side="right">{database.description}</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>

                    {expanded[database.id] && (
                      <div className="ml-6 space-y-1 mt-1">
                        {database.tables.map((table) => (
                          <div key={table.id}>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <button
                                    onClick={() => toggleExpand(table.id)}
                                    className="flex items-center w-full p-2 hover:bg-gray-100 rounded-md text-left"
                                  >
                                    {expanded[table.id] ? (
                                      <ChevronDown className="h-4 w-4 mr-1" />
                                    ) : (
                                      <ChevronRight className="h-4 w-4 mr-1" />
                                    )}
                                    <Table className="h-4 w-4 mr-2 text-green-600" /> {/* Table Icon */}
                                    <span>{table.name}</span>
                                  </button>
                                </TooltipTrigger>
                                <TooltipContent side="right">{table.description}</TooltipContent>
                              </Tooltip>
                            </TooltipProvider>

                            {expanded[table.id] && (
                              <div className="ml-6 space-y-1 mt-1">
                                {table.columns.map((column) => (
                                  <TooltipProvider key={column.id}>
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        <div className="flex items-center p-2 hover:bg-gray-100 rounded-md">
                                          <FileText className="h-4 w-4 mr-2 text-gray-500" /> {/* Column Icon */}
                                          <span>{column.name}</span>
                                          <span className="ml-2 text-xs text-gray-500">{column.type}</span>
                                        </div>
                                      </TooltipTrigger>
                                      <TooltipContent side="right">{column.description}</TooltipContent>
                                    </Tooltip>
                                  </TooltipProvider>
                                ))}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}
