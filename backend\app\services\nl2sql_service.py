"""
自然语言转SQL服务
负责将用户的自然语言查询转换为SQL语句
"""

import logging
from typing import Dict, Any, Optional, List
import json
import re
from sqlalchemy.orm import Session
import sqlparse
from sqlparse.sql import Statement
from sqlparse.tokens import Keyword, DML

from app.models.llm import LLMProvider
from app.models.metadata import MetadataDatabase, MetadataTable, MetadataColumn
from app.services.llm_service import LLMService
from app.core.exceptions import NL2SQLError

logger = logging.getLogger(__name__)

class NL2SQLService:
    """自然语言转SQL转换服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.llm_service = LLMService(db)
    
    async def convert_nl_to_sql(
        self,
        natural_query: str,
        data_source_id: int,
        user_id: int,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        将自然语言查询转换为SQL
        
        Args:
            natural_query: 自然语言查询
            data_source_id: 数据源ID
            user_id: 用户ID
            context: 额外上下文信息
            
        Returns:
            包含SQL、解释等信息的字典
        """
        try:
            logger.info(f"开始NL2SQL转换: {natural_query[:100]}...")
            
            # 1. 获取数据源的元数据信息
            metadata_context = await self._get_metadata_context(data_source_id)
            
            # 2. 构建提示词
            prompt = await self._build_prompt(
                natural_query, 
                metadata_context, 
                context
            )
            
            # 3. 调用LLM生成SQL
            llm_response = await self.llm_service.generate_sql(
                prompt=prompt,
                user_id=user_id
            )
            
            # 4. 解析和验证SQL
            parsed_result = await self._parse_llm_response(llm_response)
            
            # 5. SQL语法验证
            validated_sql = await self._validate_sql(
                parsed_result["sql"], 
                data_source_id
            )
            
            result = {
                "success": True,
                "sql": validated_sql,
                "explanation": parsed_result.get("explanation", ""),
                "confidence": parsed_result.get("confidence", 0.8),
                "tables_used": parsed_result.get("tables_used", []),
                "columns_used": parsed_result.get("columns_used", []),
                "query_type": parsed_result.get("query_type", "SELECT"),
                "estimated_rows": parsed_result.get("estimated_rows", None)
            }
            
            logger.info("NL2SQL转换成功完成")
            return result
            
        except Exception as e:
            logger.error(f"NL2SQL转换失败: {e}", exc_info=True)
            raise NL2SQLError(f"查询转换失败: {str(e)}")
    
    async def _get_metadata_context(self, data_source_id: int) -> Dict[str, Any]:
        """获取数据源的元数据上下文"""
        try:
            # 查询数据源的所有数据库
            databases = self.db.query(MetadataDatabase).filter(
                MetadataDatabase.data_source_id == data_source_id
            ).all()
            
            metadata_context = {
                "databases": [],
                "total_tables": 0,
                "total_columns": 0
            }
            
            for db in databases:
                db_info = {
                    "name": db.name,
                    "description": db.description,
                    "tables": []
                }
                
                # 查询数据库的所有表
                tables = self.db.query(MetadataTable).filter(
                    MetadataTable.database_id == db.id
                ).all()
                
                for table in tables:
                    table_info = {
                        "name": table.name,
                        "description": table.description,
                        "row_count": table.row_count,
                        "columns": []
                    }
                    
                    # 查询表的所有列
                    columns = self.db.query(MetadataColumn).filter(
                        MetadataColumn.table_id == table.id
                    ).order_by(MetadataColumn.column_order).all()
                    
                    for column in columns:
                        column_info = {
                            "name": column.name,
                            "type": column.data_type,
                            "nullable": column.is_nullable,
                            "primary_key": column.is_primary_key,
                            "description": column.description
                        }
                        table_info["columns"].append(column_info)
                    
                    db_info["tables"].append(table_info)
                    metadata_context["total_tables"] += 1
                    metadata_context["total_columns"] += len(columns)
                
                metadata_context["databases"].append(db_info)
            
            return metadata_context
            
        except Exception as e:
            logger.error(f"获取元数据上下文失败: {e}")
            raise
    
    async def _build_prompt(
        self, 
        natural_query: str, 
        metadata_context: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """构建发送给LLM的提示词"""
        
        # 构建数据库结构描述
        schema_description = self._format_schema_description(metadata_context)
        
        # 基础提示词模板
        base_prompt = f"""
        你是一个专业的SQL查询生成助手。请根据用户的自然语言查询和提供的数据库结构信息，生成准确的SQL查询语句。

        ## 数据库结构信息：
        {schema_description}

        ## 用户查询：
        {natural_query}

        ## 要求：
        1. 生成的SQL必须语法正确且可执行
        2. 只使用提供的数据库结构中存在的表和字段
        3. 优先使用有描述信息的字段进行匹配
        4. 如果查询涉及时间范围，请合理推断时间条件
        5. 对于聚合查询，请添加适当的GROUP BY子句
        6. 对于排序查询，请添加ORDER BY子句
        7. 限制返回结果数量，默认添加LIMIT 100

        ## 响应格式（请严格按照JSON格式返回）：
        {{
            "sql": "生成的SQL语句",
            "explanation": "SQL语句的中文解释",
            "confidence": 0.95,
            "tables_used": ["使用的表名列表"],
            "columns_used": ["使用的字段名列表"],
            "query_type": "SELECT|INSERT|UPDATE|DELETE",
            "estimated_rows": 预估返回行数
        }}
        """
        
        # 添加额外上下文
        if context:
            if context.get("previous_queries"):
                base_prompt += f"\n## 历史查询参考：\n{context['previous_queries']}"
            
            if context.get("user_preferences"):
                base_prompt += f"\n## 用户偏好：\n{context['user_preferences']}"
        
        return base_prompt
    
    def _format_schema_description(self, metadata_context: Dict[str, Any]) -> str:
        """格式化数据库结构描述"""
        description_parts = []
        
        for db in metadata_context["databases"]:
            db_part = f"\n### 数据库: {db['name']}"
            if db["description"]:
                db_part += f"\n描述: {db['description']}"
            
            for table in db["tables"]:
                table_part = f"\n\n#### 表: {table['name']}"
                if table["description"]:
                    table_part += f"\n描述: {table['description']}"
                if table["row_count"]:
                    table_part += f"\n行数: {table['row_count']:,}"
                
                table_part += "\n字段:"
                for column in table["columns"]:
                    column_desc = f"\n  - {column['name']} ({column['type']})"
                    if column["primary_key"]:
                        column_desc += " [主键]"
                    if not column["nullable"]:
                        column_desc += " [非空]"
                    if column["description"]:
                        column_desc += f" - {column['description']}"
                    table_part += column_desc
                
                db_part += table_part
            
            description_parts.append(db_part)
        
        return "\n".join(description_parts)
    
    async def _parse_llm_response(self, llm_response: str) -> Dict[str, Any]:
        """解析LLM返回的响应"""
        try:
            # 尝试直接解析JSON
            if llm_response.strip().startswith("{"):
                return json.loads(llm_response)
            
            # 尝试从文本中提取JSON
            json_match = re.search(r'\{.*\}', llm_response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            
            # 如果无法解析JSON，尝试提取SQL
            sql_match = re.search(r'```sql\n(.*?)\n```', llm_response, re.DOTALL)
            if sql_match:
                return {
                    "sql": sql_match.group(1).strip(),
                    "explanation": "LLM生成的SQL查询",
                    "confidence": 0.7,
                    "tables_used": [],
                    "columns_used": [],
                    "query_type": "SELECT"
                }
            
            # 最后尝试直接使用响应作为SQL
            return {
                "sql": llm_response.strip(),
                "explanation": "LLM生成的SQL查询",
                "confidence": 0.5,
                "tables_used": [],
                "columns_used": [],
                "query_type": "SELECT"
            }
            
        except json.JSONDecodeError as e:
            logger.error(f"解析LLM响应失败: {e}")
            raise NL2SQLError("无法解析LLM响应")
    
    async def _validate_sql(self, sql: str, data_source_id: int) -> str:
        """增强的SQL验证和安全检查"""
        try:
            # 解析SQL语句
            parsed = sqlparse.parse(sql)
            if not parsed:
                raise NL2SQLError("无法解析SQL语句")
            
            statement = parsed[0]
            
            # 检查是否为SELECT语句
            if not self._is_select_statement(statement):
                raise NL2SQLError("只允许执行SELECT查询语句")
            
            # 检查危险函数和关键词
            dangerous_patterns = [
                r'LOAD_FILE\s*\(',
                r'INTO\s+OUTFILE',
                r'INTO\s+DUMPFILE',
                r'BENCHMARK\s*\(',
                r'SLEEP\s*\(',
                r'USER\s*\(',
                r'VERSION\s*\(',
                r'DATABASE\s*\(',
            ]
            
            sql_upper = sql.upper()
            for pattern in dangerous_patterns:
                if re.search(pattern, sql_upper):
                    raise NL2SQLError(f"检测到危险SQL模式: {pattern}")
            
            # 验证表名和字段名是否存在于元数据中
            await self._validate_schema_objects(statement, data_source_id)
            
            # 添加行数限制
            if not re.search(r'LIMIT\s+\d+', sql_upper):
                sql += f' LIMIT {settings.MAX_QUERY_RESULT_ROWS}'
            
            return sql.strip()
            
        except Exception as e:
            logger.error(f"SQL验证失败: {e}")
            raise NL2SQLError(f"SQL验证失败: {str(e)}")

    def _is_select_statement(self, statement: Statement) -> bool:
        """检查是否为SELECT语句"""
        for token in statement.flatten():
            if token.ttype is Keyword and token.value.upper() == 'SELECT':
                return True
            elif token.ttype is DML:
                return token.value.upper() == 'SELECT'
        return False
