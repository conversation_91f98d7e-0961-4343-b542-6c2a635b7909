"use client"

import { useState } from "react"
import { Clock, Star, StarOff } from "lucide-react"
// Removed Tabs, <PERSON>bsContent, TabsList, TabsTrigger imports as they are no longer needed

// Mock history data
const mockHistory = [
  {
    id: "h1",
    query: "查询过去30天销售额最高的10个产品",
    timestamp: "2023-07-15 14:30",
    isFavorite: true,
  },
  {
    id: "h2",
    query: "统计各地区客户数量分布",
    timestamp: "2023-07-14 10:15",
    isFavorite: false,
  },
  {
    id: "h3",
    query: "分析不同产品类别的销售趋势",
    timestamp: "2023-07-13 16:45",
    isFavorite: true,
  },
  {
    id: "h4",
    query: "查询客户购买频率最高的产品",
    timestamp: "2023-07-12 09:20",
    isFavorite: false,
  },
  {
    id: "h5",
    query: "统计每月销售额变化情况",
    timestamp: "2023-07-11 11:05",
    isFavorite: false,
  },
]

export default function QueryHistory() {
  const [history, setHistory] = useState(mockHistory)

  const toggleFavorite = (id: string) => {
    setHistory((prev) => prev.map((item) => (item.id === id ? { ...item, isFavorite: !item.isFavorite } : item)))
  }

  const filteredHistory = history

  return (
    <div className="p-2">
      <div className="text-sm font-medium text-gray-500 mb-2 px-2">历史查询记录</div> {/* Added a title */}
      <div className="space-y-2">
        {filteredHistory.map((item) => (
          <div key={item.id} className="p-3 text-sm border border-gray-100 rounded-md hover:bg-gray-50 cursor-pointer">
            <div className="flex justify-between items-start">
              <div className="font-medium line-clamp-2">{item.query}</div>
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  toggleFavorite(item.id)
                }}
                className="ml-2 flex-shrink-0"
              >
                {item.isFavorite ? (
                  <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                ) : (
                  <StarOff className="h-4 w-4 text-gray-400" />
                )}
              </button>
            </div>
            <div className="flex items-center mt-2 text-xs text-gray-500">
              <Clock className="h-3 w-3 mr-1" />
              {item.timestamp}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
