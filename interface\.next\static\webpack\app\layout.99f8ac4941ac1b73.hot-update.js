"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"a2e252a97906\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkQ6XFx3b3Jrc3BhY2VcXGRzdHBcXHByb2plY3RcXG5sMnNxbFxcaW50ZXJmYWNlXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYTJlMjUyYTk3OTA2XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn() {\n    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){\n        inputs[_key] = arguments[_key];\n    }\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n// API配置\nconst API_BASE_URL = \"http://localhost:8000/api/v1\" || 0;\n// API客户端类\nclass ApiClient {\n    setToken(token) {\n        this.token = token;\n        if (true) {\n            localStorage.setItem('access_token', token);\n        }\n    }\n    clearToken() {\n        this.token = null;\n        if (true) {\n            localStorage.removeItem('access_token');\n        }\n    }\n    async request(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const url = \"\".concat(this.baseURL).concat(endpoint);\n        const headers = {\n            'Content-Type': 'application/json',\n            ...options.headers\n        };\n        if (this.token) {\n            headers.Authorization = \"Bearer \".concat(this.token);\n        }\n        const config = {\n            ...options,\n            headers\n        };\n        try {\n            const response = await fetch(url, config);\n            if (!response.ok) {\n                if (response.status === 401) {\n                    // Token过期，清除token\n                    this.clearToken();\n                    throw new Error('认证失败，请重新登录');\n                }\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            return await response.json();\n        } catch (error) {\n            console.error('API request failed:', error);\n            throw error;\n        }\n    }\n    // GET请求\n    async get(endpoint) {\n        return this.request(endpoint, {\n            method: 'GET'\n        });\n    }\n    // POST请求\n    async post(endpoint, data) {\n        return this.request(endpoint, {\n            method: 'POST',\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n    // PUT请求\n    async put(endpoint, data) {\n        return this.request(endpoint, {\n            method: 'PUT',\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n    // DELETE请求\n    async delete(endpoint) {\n        return this.request(endpoint, {\n            method: 'DELETE'\n        });\n    }\n    // 登录\n    async login(username, password) {\n        const formData = new FormData();\n        formData.append('username', username);\n        formData.append('password', password);\n        const response = await fetch(\"\".concat(this.baseURL, \"/auth/login\"), {\n            method: 'POST',\n            body: formData\n        });\n        if (!response.ok) {\n            throw new Error('登录失败');\n        }\n        const data = await response.json();\n        this.setToken(data.access_token);\n        return data;\n    }\n    // 注册\n    async register(userData) {\n        return this.post('/auth/register', userData);\n    }\n    // 自然语言转SQL并执行\n    async nlToSqlAndExecute(query) {\n        let dataSourceId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n        const response = await this.post('/query/nl2sql-and-execute', {\n            natural_query: query,\n            data_source_id: dataSourceId\n        });\n        // 处理后端返回的嵌套数据结构\n        if (response.success && response.data) {\n            return {\n                success: true,\n                data: response.data.data,\n                columns: response.data.columns,\n                sql: response.data.generated_sql,\n                message: response.message\n            };\n        }\n        return response;\n    }\n    // 执行SQL查询\n    async executeQuery(sql) {\n        let dataSourceId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n        return this.post('/query/execute', {\n            sql,\n            data_source_id: dataSourceId\n        });\n    }\n    // 获取数据源列表\n    async getDataSources() {\n        return this.get('/data-sources/');\n    }\n    // 获取查询历史\n    async getQueryHistory() {\n        return this.get('/query/history');\n    }\n    // 获取收藏查询\n    async getFavoriteQueries() {\n        return this.get('/favorites/');\n    }\n    // 获取系统健康状态\n    async getSystemHealth() {\n        return this.get('/system/health');\n    }\n    constructor(baseURL){\n        this.token = null;\n        this.baseURL = baseURL;\n        // 从localStorage获取token\n        if (true) {\n            this.token = localStorage.getItem('access_token');\n        }\n    }\n}\n// 创建全局API客户端实例\nconst apiClient = new ApiClient(API_BASE_URL);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/utils.ts\n"));

/***/ })

});