"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/table.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-no-axes-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-line.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-pie.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-down.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./components/ui/tooltip.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_metadata_explorer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/metadata-explorer */ \"(app-pages-browser)/./components/metadata-explorer.tsx\");\n/* harmony import */ var _components_query_history__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/query-history */ \"(app-pages-browser)/./components/query-history.tsx\");\n/* harmony import */ var _components_results_display__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/results-display */ \"(app-pages-browser)/./components/results-display.tsx\");\n/* harmony import */ var _components_favorite_queries__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/favorite-queries */ \"(app-pages-browser)/./components/favorite-queries.tsx\");\n/* harmony import */ var _components_sql_editor__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/sql-editor */ \"(app-pages-browser)/./components/sql-editor.tsx\");\n/* harmony import */ var _components_example_queries__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/example-queries */ \"(app-pages-browser)/./components/example-queries.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n // Corrected import path\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"metadata\");\n    const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [sqlQuery, setSqlQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"table\");\n    const [chartType, setChartType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"bar\");\n    const [inputMode, setInputMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"nl\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (inputMode === \"nl\" && !query.trim()) return;\n        if (inputMode === \"sql\" && !sqlQuery.trim()) return;\n        setIsProcessing(true);\n        try {\n            let response;\n            if (inputMode === \"nl\") {\n                // 自然语言转SQL并执行\n                response = await _lib_utils__WEBPACK_IMPORTED_MODULE_12__.apiClient.nlToSqlAndExecute(query);\n                if (response.success) {\n                    // 设置生成的SQL\n                    if (response.sql) {\n                        setSqlQuery(response.sql);\n                    }\n                    // 设置查询结果\n                    if (response.data) {\n                        setResults({\n                            columns: response.columns || Object.keys(response.data[0] || {}),\n                            data: response.data\n                        });\n                    }\n                    // 切换到SQL编辑器模式\n                    setInputMode(\"sql\");\n                } else {\n                    console.error('查询失败:', response.message);\n                // 这里可以添加错误提示\n                }\n            } else {\n                // 执行SQL查询\n                response = await _lib_utils__WEBPACK_IMPORTED_MODULE_12__.apiClient.executeQuery(sqlQuery);\n                if (response.success && response.data) {\n                    setResults({\n                        columns: response.columns || Object.keys(response.data[0] || {}),\n                        data: response.data\n                    });\n                } else {\n                    console.error('SQL执行失败:', response.message);\n                // 这里可以添加错误提示\n                }\n            }\n        } catch (error) {\n            console.error('API调用失败:', error);\n            // 如果API调用失败，回退到模拟数据\n            const generatedSql = inputMode === \"nl\" ? \"SELECT product_name, SUM(sales_amount) as total_sales FROM sales_data GROUP BY product_name ORDER BY total_sales DESC LIMIT 10;\" : sqlQuery;\n            setSqlQuery(generatedSql);\n            setResults({\n                columns: [\n                    \"product_name\",\n                    \"total_sales\"\n                ],\n                data: [\n                    {\n                        product_name: \"Product A\",\n                        total_sales: 12500\n                    },\n                    {\n                        product_name: \"Product B\",\n                        total_sales: 9800\n                    },\n                    {\n                        product_name: \"Product C\",\n                        total_sales: 7600\n                    },\n                    {\n                        product_name: \"Product D\",\n                        total_sales: 6200\n                    },\n                    {\n                        product_name: \"Product E\",\n                        total_sales: 5100\n                    }\n                ]\n            });\n            if (inputMode === \"nl\") {\n                setInputMode(\"sql\");\n            }\n        } finally{\n            setIsProcessing(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-screen bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-1 overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"relative flex flex-col border-r transition-all duration-300 bg-gray-50 opacity-100 border-slate-50\", sidebarOpen ? \"w-72\" : \"w-0\"),\n                    children: [\n                        sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative mx-2 mt-4 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                    type: \"text\",\n                                    placeholder: \"搜索库表、历史查询...\",\n                                    className: \"w-full pl-9 pr-3 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-1 focus:ring-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 13\n                        }, this),\n                        sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                            value: activeTab,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsList, {\n                                    className: \"grid grid-cols-3 mx-2 mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                            value: \"metadata\",\n                                            onClick: ()=>setActiveTab(\"metadata\"),\n                                            children: \"元数据\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                            value: \"favorites\",\n                                            onClick: ()=>setActiveTab(\"favorites\"),\n                                            children: \"收藏夹\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                            value: \"history\",\n                                            onClick: ()=>setActiveTab(\"history\"),\n                                            children: \"历史查询\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                    value: \"metadata\",\n                                    className: \"flex-1 p-0 m-0 pb-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                                        className: \"h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_metadata_explorer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                    value: \"favorites\",\n                                    className: \"flex-1 p-0 m-0 pb-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                                        className: \"h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_favorite_queries__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                    value: \"history\",\n                                    className: \"flex-1 p-0 m-0 pb-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                                        className: \"h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_query_history__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 13\n                        }, this),\n                        sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-0 left-0 right-0 p-2 bg-gray-50 border-t border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    onClick: ()=>setSidebarOpen(false),\n                                    className: \"h-8 text-gray-600 hover:text-gray-900 hover:bg-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex flex-col h-full overflow-hidden\",\n                    children: [\n                        !sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"absolute bottom-2 left-0 z-10 p-1 bg-white border border-gray-200 rounded-r-md\",\n                            onClick: ()=>setSidebarOpen(!sidebarOpen),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                size: 18\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-auto p-6\",\n                            children: !results ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full flex flex-col items-center justify-center text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        size: 48,\n                                        className: \"mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-medium mb-2\",\n                                        children: \"欢迎使用 NL2SQL 数据智能分析系统\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-center max-w-md mb-8\",\n                                        children: [\n                                            \"输入自然语言查询，系统将自动转换为 SQL 并从数据仓库中检索结果。\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"您可以在左侧浏览数据仓库的元数据结构。\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_example_queries__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        onSelectQuery: (q)=>{\n                                            setQuery(q);\n                                            setInputMode(\"nl\");\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg border border-gray-200 overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium\",\n                                                        children: \"查询结果\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"icon\",\n                                                                                onClick: ()=>setViewMode(\"table\"),\n                                                                                className: viewMode === \"table\" ? \"bg-gray-100\" : \"\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 234,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 228,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 227,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                            children: \"表格视图\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 237,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 226,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"icon\",\n                                                                                onClick: ()=>{\n                                                                                    setViewMode(\"chart\");\n                                                                                    setChartType(\"bar\");\n                                                                                },\n                                                                                className: viewMode === \"chart\" && chartType === \"bar\" ? \"bg-gray-100\" : \"\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 253,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 244,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 243,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                            children: \"柱状图\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 256,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 242,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"icon\",\n                                                                                onClick: ()=>{\n                                                                                    setViewMode(\"chart\");\n                                                                                    setChartType(\"line\");\n                                                                                },\n                                                                                className: viewMode === \"chart\" && chartType === \"line\" ? \"bg-gray-100\" : \"\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 272,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 263,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 262,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                            children: \"折线图\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 275,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 261,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"icon\",\n                                                                                onClick: ()=>{\n                                                                                    setViewMode(\"chart\");\n                                                                                    setChartType(\"pie\");\n                                                                                },\n                                                                                className: viewMode === \"chart\" && chartType === \"pie\" ? \"bg-gray-100\" : \"\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 291,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 282,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 281,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                            children: \"饼图\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 294,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 280,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"icon\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 302,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 301,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 300,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                            children: \"下载结果\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 305,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 299,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_results_display__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                results: results,\n                                                viewMode: viewMode,\n                                                chartType: chartType\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"收藏查询\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"结果准确吗��\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"icon\",\n                                                        className: \"h-8 w-8 bg-transparent\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"icon\",\n                                                        className: \"h-8 w-8 bg-transparent\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t p-4 bg-white border-white\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"flex flex-col\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative bg-white rounded-lg overflow-hidden border border-gray-300\",\n                                    children: inputMode === \"nl\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: query,\n                                                onChange: (e)=>setQuery(e.target.value),\n                                                placeholder: \"有问题，尽管问，shift+enter换行\",\n                                                className: \"w-full pt-4 pr-4 pl-4 pb-[56px] resize-none bg-white text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 overflow-y-auto\",\n                                                rows: 5,\n                                                onKeyDown: (e)=>{\n                                                    if (e.key === \"Enter\" && e.shiftKey) {\n                                                        return; // Allow shift+enter for new line\n                                                    } else if (e.key === \"Enter\") {\n                                                        e.preventDefault();\n                                                        handleSubmit(e);\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-0 left-0 right-0 flex items-center justify-between p-3 border-t border-white opacity-100 text-white bg-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: inputMode,\n                                                            onChange: (e)=>setInputMode(e.target.value),\n                                                            className: \"border rounded-full px-3 py-1.5 focus:outline-none focus:ring-1 focus:ring-blue-500 text-slate-950 text-xs tracking-tight leading-7 font-medium bg-slate-200 border-slate-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"nl\",\n                                                                    children: \"自然语言查询\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 363,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"sql\",\n                                                                    children: \"SQL 编辑器\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 364,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            type: \"submit\",\n                                                            size: \"icon\",\n                                                            className: \"bg-blue-600 hover:bg-blue-700 text-white rounded-full h-8 w-8\",\n                                                            disabled: isProcessing || !query.trim(),\n                                                            children: isProcessing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sql_editor__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    value: sqlQuery,\n                                                    onChange: setSqlQuery,\n                                                    placeholder: \"输入 SQL 查询语句...\",\n                                                    height: \"141px\",\n                                                    darkMode: false\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-3 border-t bg-white border-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: inputMode,\n                                                            onChange: (e)=>setInputMode(e.target.value),\n                                                            className: \"border rounded-full px-3 py-1.5 focus:outline-none focus:ring-1 focus:ring-blue-500 text-xs font-medium tracking-tight bg-slate-200 border-slate-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"nl\",\n                                                                    children: \"自然语言查询\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 402,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"sql\",\n                                                                    children: \"SQL 编辑器\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 403,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            type: \"submit\",\n                                                            size: \"icon\",\n                                                            className: \"bg-blue-600 hover:bg-blue-700 text-white rounded-full h-8 w-8\",\n                                                            disabled: isProcessing || !sqlQuery.trim(),\n                                                            children: isProcessing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 417,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n            lineNumber: 124,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"gqcRfK0wLink7NnsKtYLKUU2kFs=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});