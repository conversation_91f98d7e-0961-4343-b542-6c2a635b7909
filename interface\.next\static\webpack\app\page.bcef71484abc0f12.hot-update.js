"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/table.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-no-axes-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-line.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-pie.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-down.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./components/ui/tooltip.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_metadata_explorer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/metadata-explorer */ \"(app-pages-browser)/./components/metadata-explorer.tsx\");\n/* harmony import */ var _components_query_history__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/query-history */ \"(app-pages-browser)/./components/query-history.tsx\");\n/* harmony import */ var _components_results_display__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/results-display */ \"(app-pages-browser)/./components/results-display.tsx\");\n/* harmony import */ var _components_favorite_queries__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/favorite-queries */ \"(app-pages-browser)/./components/favorite-queries.tsx\");\n/* harmony import */ var _components_sql_editor__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/sql-editor */ \"(app-pages-browser)/./components/sql-editor.tsx\");\n/* harmony import */ var _components_example_queries__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/example-queries */ \"(app-pages-browser)/./components/example-queries.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n // Corrected import path\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"metadata\");\n    const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [sqlQuery, setSqlQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"table\");\n    const [chartType, setChartType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"bar\");\n    const [inputMode, setInputMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"nl\");\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (inputMode === \"nl\" && !query.trim()) return;\n        if (inputMode === \"sql\" && !sqlQuery.trim()) return;\n        setIsProcessing(true);\n        try {\n            let response;\n            if (inputMode === \"nl\") {\n                // 自然语言转SQL并执行\n                response = await _lib_utils__WEBPACK_IMPORTED_MODULE_12__.apiClient.nlToSqlAndExecute(query);\n                if (response.success) {\n                    // 设置生成的SQL\n                    if (response.sql) {\n                        setSqlQuery(response.sql);\n                    }\n                    // 设置查询结果\n                    if (response.data) {\n                        setResults({\n                            columns: response.columns || Object.keys(response.data[0] || {}),\n                            data: response.data\n                        });\n                    }\n                    // 切换到SQL编辑器模式\n                    setInputMode(\"sql\");\n                } else {\n                    console.error('查询失败:', response.message);\n                // 这里可以添加错误提示\n                }\n            } else {\n                // 执行SQL查询\n                response = await _lib_utils__WEBPACK_IMPORTED_MODULE_12__.apiClient.executeQuery(sqlQuery);\n                if (response.success && response.data) {\n                    setResults({\n                        columns: response.columns || Object.keys(response.data[0] || {}),\n                        data: response.data\n                    });\n                } else {\n                    console.error('SQL执行失败:', response.message);\n                // 这里可以添加错误提示\n                }\n            }\n        } catch (error) {\n            console.error('API调用失败:', error);\n            // 如果API调用失败，回退到模拟数据\n            const generatedSql = inputMode === \"nl\" ? \"SELECT product_name, SUM(sales_amount) as total_sales FROM sales_data GROUP BY product_name ORDER BY total_sales DESC LIMIT 10;\" : sqlQuery;\n            setSqlQuery(generatedSql);\n            setResults({\n                columns: [\n                    \"product_name\",\n                    \"total_sales\"\n                ],\n                data: [\n                    {\n                        product_name: \"Product A\",\n                        total_sales: 12500\n                    },\n                    {\n                        product_name: \"Product B\",\n                        total_sales: 9800\n                    },\n                    {\n                        product_name: \"Product C\",\n                        total_sales: 7600\n                    },\n                    {\n                        product_name: \"Product D\",\n                        total_sales: 6200\n                    },\n                    {\n                        product_name: \"Product E\",\n                        total_sales: 5100\n                    }\n                ]\n            });\n            if (inputMode === \"nl\") {\n                setInputMode(\"sql\");\n            }\n        } finally{\n            setIsProcessing(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-screen bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-1 overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"relative flex flex-col border-r transition-all duration-300 bg-gray-50 opacity-100 border-slate-50\", sidebarOpen ? \"w-72\" : \"w-0\"),\n                    children: [\n                        sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative mx-2 mt-4 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                    type: \"text\",\n                                    placeholder: \"搜索库表、历史查询...\",\n                                    className: \"w-full pl-9 pr-3 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-1 focus:ring-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 13\n                        }, this),\n                        sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                            value: activeTab,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsList, {\n                                    className: \"grid grid-cols-3 mx-2 mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                            value: \"metadata\",\n                                            onClick: ()=>setActiveTab(\"metadata\"),\n                                            children: \"元数据\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                            value: \"favorites\",\n                                            onClick: ()=>setActiveTab(\"favorites\"),\n                                            children: \"收藏夹\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                            value: \"history\",\n                                            onClick: ()=>setActiveTab(\"history\"),\n                                            children: \"历史查询\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                    value: \"metadata\",\n                                    className: \"flex-1 p-0 m-0 pb-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                                        className: \"h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_metadata_explorer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                    value: \"favorites\",\n                                    className: \"flex-1 p-0 m-0 pb-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                                        className: \"h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_favorite_queries__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                    value: \"history\",\n                                    className: \"flex-1 p-0 m-0 pb-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                                        className: \"h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_query_history__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, this),\n                        sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-0 left-0 right-0 p-2 bg-gray-50 border-t border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    onClick: ()=>setSidebarOpen(false),\n                                    className: \"h-8 text-gray-600 hover:text-gray-900 hover:bg-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex flex-col h-full overflow-hidden\",\n                    children: [\n                        !sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"absolute bottom-2 left-0 z-10 p-1 bg-white border border-gray-200 rounded-r-md\",\n                            onClick: ()=>setSidebarOpen(!sidebarOpen),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                size: 18\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-auto p-6\",\n                            children: !results ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full flex flex-col items-center justify-center text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        size: 48,\n                                        className: \"mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-medium mb-2\",\n                                        children: \"欢迎使用 NL2SQL 数据智能分析系统\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-center max-w-md mb-8\",\n                                        children: [\n                                            \"输入自然语言查询，系统将自动转换为 SQL 并从数据仓库中检索结果。\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"您可以在左侧浏览数据仓库的元数据结构。\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_example_queries__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        onSelectQuery: (q)=>{\n                                            setQuery(q);\n                                            setInputMode(\"nl\");\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg border border-gray-200 overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium\",\n                                                        children: \"查询结果\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"icon\",\n                                                                                onClick: ()=>setViewMode(\"table\"),\n                                                                                className: viewMode === \"table\" ? \"bg-gray-100\" : \"\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 233,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 227,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 226,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                            children: \"表格视图\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 236,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 225,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"icon\",\n                                                                                onClick: ()=>{\n                                                                                    setViewMode(\"chart\");\n                                                                                    setChartType(\"bar\");\n                                                                                },\n                                                                                className: viewMode === \"chart\" && chartType === \"bar\" ? \"bg-gray-100\" : \"\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 252,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 243,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 242,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                            children: \"柱状图\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 255,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 241,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"icon\",\n                                                                                onClick: ()=>{\n                                                                                    setViewMode(\"chart\");\n                                                                                    setChartType(\"line\");\n                                                                                },\n                                                                                className: viewMode === \"chart\" && chartType === \"line\" ? \"bg-gray-100\" : \"\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 271,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 262,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 261,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                            children: \"折线图\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 274,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 260,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"icon\",\n                                                                                onClick: ()=>{\n                                                                                    setViewMode(\"chart\");\n                                                                                    setChartType(\"pie\");\n                                                                                },\n                                                                                className: viewMode === \"chart\" && chartType === \"pie\" ? \"bg-gray-100\" : \"\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 290,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 281,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 280,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                            children: \"饼图\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 293,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 279,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"icon\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 301,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 300,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 299,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                            children: \"下载结果\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 304,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 298,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_results_display__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                results: results,\n                                                viewMode: viewMode,\n                                                chartType: chartType\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"收藏查询\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"结果准确吗��\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"icon\",\n                                                        className: \"h-8 w-8 bg-transparent\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"icon\",\n                                                        className: \"h-8 w-8 bg-transparent\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t p-4 bg-white border-white\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"flex flex-col\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative bg-white rounded-lg overflow-hidden border border-gray-300\",\n                                    children: inputMode === \"nl\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: query,\n                                                onChange: (e)=>setQuery(e.target.value),\n                                                placeholder: \"有问题，尽管问，shift+enter换行\",\n                                                className: \"w-full pt-4 pr-4 pl-4 pb-[56px] resize-none bg-white text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 overflow-y-auto\",\n                                                rows: 5,\n                                                onKeyDown: (e)=>{\n                                                    if (e.key === \"Enter\" && e.shiftKey) {\n                                                        return; // Allow shift+enter for new line\n                                                    } else if (e.key === \"Enter\") {\n                                                        e.preventDefault();\n                                                        handleSubmit(e);\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-0 left-0 right-0 flex items-center justify-between p-3 border-t border-white opacity-100 text-white bg-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: inputMode,\n                                                            onChange: (e)=>setInputMode(e.target.value),\n                                                            className: \"border rounded-full px-3 py-1.5 focus:outline-none focus:ring-1 focus:ring-blue-500 text-slate-950 text-xs tracking-tight leading-7 font-medium bg-slate-200 border-slate-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"nl\",\n                                                                    children: \"自然语言查询\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 362,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"sql\",\n                                                                    children: \"SQL 编辑器\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 363,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            type: \"submit\",\n                                                            size: \"icon\",\n                                                            className: \"bg-blue-600 hover:bg-blue-700 text-white rounded-full h-8 w-8\",\n                                                            disabled: isProcessing || !query.trim(),\n                                                            children: isProcessing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 368,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sql_editor__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    value: sqlQuery,\n                                                    onChange: setSqlQuery,\n                                                    placeholder: \"输入 SQL 查询语句...\",\n                                                    height: \"141px\",\n                                                    darkMode: false\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-3 border-t bg-white border-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: inputMode,\n                                                            onChange: (e)=>setInputMode(e.target.value),\n                                                            className: \"border rounded-full px-3 py-1.5 focus:outline-none focus:ring-1 focus:ring-blue-500 text-xs font-medium tracking-tight bg-slate-200 border-slate-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"nl\",\n                                                                    children: \"自然语言查询\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 401,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"sql\",\n                                                                    children: \"SQL 编辑器\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 402,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 396,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            type: \"submit\",\n                                                            size: \"icon\",\n                                                            className: \"bg-blue-600 hover:bg-blue-700 text-white rounded-full h-8 w-8\",\n                                                            disabled: isProcessing || !sqlQuery.trim(),\n                                                            children: isProcessing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n            lineNumber: 123,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"bIvVp0BsJW13eqTuj8fgbx3emVo=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});