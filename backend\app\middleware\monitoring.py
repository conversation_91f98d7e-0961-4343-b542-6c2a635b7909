import time
import logging
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from typing import Callable

logger = logging.getLogger(__name__)

class PerformanceMonitoringMiddleware(BaseHTTPMiddleware):
    """性能监控中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        
        # 记录请求信息
        logger.info(f"请求开始: {request.method} {request.url}")
        
        try:
            response = await call_next(request)
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 添加响应头
            response.headers["X-Process-Time"] = str(process_time)
            
            # 记录响应信息
            logger.info(
                f"请求完成: {request.method} {request.url} "
                f"状态码: {response.status_code} "
                f"处理时间: {process_time:.3f}s"
            )
            
            # 慢查询告警
            if process_time > 5.0:
                logger.warning(
                    f"慢请求检测: {request.method} {request.url} "
                    f"处理时间: {process_time:.3f}s"
                )
            
            return response
            
        except Exception as e:
            process_time = time.time() - start_time
            logger.error(
                f"请求异常: {request.method} {request.url} "
                f"错误: {str(e)} "
                f"处理时间: {process_time:.3f}s"
            )
            raise