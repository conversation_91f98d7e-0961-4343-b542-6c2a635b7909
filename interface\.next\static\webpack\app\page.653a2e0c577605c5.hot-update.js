"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/table.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-no-axes-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-line.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-pie.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-down.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./components/ui/tooltip.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_metadata_explorer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/metadata-explorer */ \"(app-pages-browser)/./components/metadata-explorer.tsx\");\n/* harmony import */ var _components_query_history__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/query-history */ \"(app-pages-browser)/./components/query-history.tsx\");\n/* harmony import */ var _components_results_display__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/results-display */ \"(app-pages-browser)/./components/results-display.tsx\");\n/* harmony import */ var _components_favorite_queries__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/favorite-queries */ \"(app-pages-browser)/./components/favorite-queries.tsx\");\n/* harmony import */ var _components_sql_editor__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/sql-editor */ \"(app-pages-browser)/./components/sql-editor.tsx\");\n/* harmony import */ var _components_example_queries__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/example-queries */ \"(app-pages-browser)/./components/example-queries.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n // Corrected import path\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"metadata\");\n    const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [sqlQuery, setSqlQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"table\");\n    const [chartType, setChartType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"bar\");\n    const [inputMode, setInputMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"nl\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (inputMode === \"nl\" && !query.trim()) return;\n        if (inputMode === \"sql\" && !sqlQuery.trim()) return;\n        setIsProcessing(true);\n        setError(\"\");\n        try {\n            let response;\n            if (inputMode === \"nl\") {\n                // 自然语言转SQL并执行\n                response = await _lib_utils__WEBPACK_IMPORTED_MODULE_12__.apiClient.nlToSqlAndExecute(query);\n                if (response.success) {\n                    // 设置生成的SQL\n                    if (response.sql) {\n                        setSqlQuery(response.sql);\n                    }\n                    // 设置查询结果\n                    if (response.data) {\n                        setResults({\n                            columns: response.columns || Object.keys(response.data[0] || {}),\n                            data: response.data\n                        });\n                    }\n                    // 切换到SQL编辑器模式\n                    setInputMode(\"sql\");\n                } else {\n                    setError(response.message || '查询失败');\n                }\n            } else {\n                // 执行SQL查询\n                response = await _lib_utils__WEBPACK_IMPORTED_MODULE_12__.apiClient.executeQuery(sqlQuery);\n                if (response.success && response.data) {\n                    setResults({\n                        columns: response.columns || Object.keys(response.data[0] || {}),\n                        data: response.data\n                    });\n                } else {\n                    setError(response.message || 'SQL执行失败');\n                }\n            }\n        } catch (error) {\n            console.error('API调用失败:', error);\n            setError(\"连接后端失败: \".concat(error.message));\n            // 如果API调用失败，回退到模拟数据\n            const generatedSql = inputMode === \"nl\" ? \"SELECT product_name, SUM(sales_amount) as total_sales FROM sales_data GROUP BY product_name ORDER BY total_sales DESC LIMIT 10;\" : sqlQuery;\n            setSqlQuery(generatedSql);\n            setResults({\n                columns: [\n                    \"product_name\",\n                    \"total_sales\"\n                ],\n                data: [\n                    {\n                        product_name: \"Product A\",\n                        total_sales: 12500\n                    },\n                    {\n                        product_name: \"Product B\",\n                        total_sales: 9800\n                    },\n                    {\n                        product_name: \"Product C\",\n                        total_sales: 7600\n                    },\n                    {\n                        product_name: \"Product D\",\n                        total_sales: 6200\n                    },\n                    {\n                        product_name: \"Product E\",\n                        total_sales: 5100\n                    }\n                ]\n            });\n            if (inputMode === \"nl\") {\n                setInputMode(\"sql\");\n            }\n        } finally{\n            setIsProcessing(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-screen bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-1 overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"relative flex flex-col border-r transition-all duration-300 bg-gray-50 opacity-100 border-slate-50\", sidebarOpen ? \"w-72\" : \"w-0\"),\n                    children: [\n                        sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative mx-2 mt-4 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                    type: \"text\",\n                                    placeholder: \"搜索库表、历史查询...\",\n                                    className: \"w-full pl-9 pr-3 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-1 focus:ring-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 13\n                        }, this),\n                        sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                            value: activeTab,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsList, {\n                                    className: \"grid grid-cols-3 mx-2 mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                            value: \"metadata\",\n                                            onClick: ()=>setActiveTab(\"metadata\"),\n                                            children: \"元数据\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                            value: \"favorites\",\n                                            onClick: ()=>setActiveTab(\"favorites\"),\n                                            children: \"收藏夹\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                            value: \"history\",\n                                            onClick: ()=>setActiveTab(\"history\"),\n                                            children: \"历史查询\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                    value: \"metadata\",\n                                    className: \"flex-1 p-0 m-0 pb-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                                        className: \"h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_metadata_explorer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                    value: \"favorites\",\n                                    className: \"flex-1 p-0 m-0 pb-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                                        className: \"h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_favorite_queries__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                    value: \"history\",\n                                    className: \"flex-1 p-0 m-0 pb-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                                        className: \"h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_query_history__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 13\n                        }, this),\n                        sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-0 left-0 right-0 p-2 bg-gray-50 border-t border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    onClick: ()=>setSidebarOpen(false),\n                                    className: \"h-8 text-gray-600 hover:text-gray-900 hover:bg-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex flex-col h-full overflow-hidden\",\n                    children: [\n                        !sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"absolute bottom-2 left-0 z-10 p-1 bg-white border border-gray-200 rounded-r-md\",\n                            onClick: ()=>setSidebarOpen(!sidebarOpen),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                size: 18\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-auto p-6\",\n                            children: [\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 15\n                                }, this),\n                                !results ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full flex flex-col items-center justify-center text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            size: 48,\n                                            className: \"mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-medium mb-2\",\n                                            children: \"欢迎使用 NL2SQL 数据智能分析系统\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-center max-w-md mb-8\",\n                                            children: [\n                                                \"输入自然语言查询，系统将自动转换为 SQL 并从数据仓库中检索结果。\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"您可以在左侧浏览数据仓库的元数据结构。\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_example_queries__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            onSelectQuery: (q)=>{\n                                                setQuery(q);\n                                                setInputMode(\"nl\");\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg border border-gray-200 overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium\",\n                                                            children: \"查询结果\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                    variant: \"outline\",\n                                                                                    size: \"icon\",\n                                                                                    onClick: ()=>setViewMode(\"table\"),\n                                                                                    className: viewMode === \"table\" ? \"bg-gray-100\" : \"\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                        size: 16\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 240,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 234,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 233,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                children: \"表格视图\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 243,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 232,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 231,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                    variant: \"outline\",\n                                                                                    size: \"icon\",\n                                                                                    onClick: ()=>{\n                                                                                        setViewMode(\"chart\");\n                                                                                        setChartType(\"bar\");\n                                                                                    },\n                                                                                    className: viewMode === \"chart\" && chartType === \"bar\" ? \"bg-gray-100\" : \"\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        size: 16\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 259,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 250,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 249,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                children: \"柱状图\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 262,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 248,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 247,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                    variant: \"outline\",\n                                                                                    size: \"icon\",\n                                                                                    onClick: ()=>{\n                                                                                        setViewMode(\"chart\");\n                                                                                        setChartType(\"line\");\n                                                                                    },\n                                                                                    className: viewMode === \"chart\" && chartType === \"line\" ? \"bg-gray-100\" : \"\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                        size: 16\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 278,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 269,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 268,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                children: \"折线图\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 281,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 267,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                    variant: \"outline\",\n                                                                                    size: \"icon\",\n                                                                                    onClick: ()=>{\n                                                                                        setViewMode(\"chart\");\n                                                                                        setChartType(\"pie\");\n                                                                                    },\n                                                                                    className: viewMode === \"chart\" && chartType === \"pie\" ? \"bg-gray-100\" : \"\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                        size: 16\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 297,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 288,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 287,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                children: \"饼图\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 300,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 286,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 285,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                    variant: \"outline\",\n                                                                                    size: \"icon\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        size: 16\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 308,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 307,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 306,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                children: \"下载结果\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 311,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 305,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 304,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_results_display__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    results: results,\n                                                    viewMode: viewMode,\n                                                    chartType: chartType\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"收藏查询\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"结果准确吗��\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"icon\",\n                                                            className: \"h-8 w-8 bg-transparent\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"icon\",\n                                                            className: \"h-8 w-8 bg-transparent\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t p-4 bg-white border-white\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"flex flex-col\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative bg-white rounded-lg overflow-hidden border border-gray-300\",\n                                    children: inputMode === \"nl\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: query,\n                                                onChange: (e)=>setQuery(e.target.value),\n                                                placeholder: \"有问题，尽管问，shift+enter换行\",\n                                                className: \"w-full pt-4 pr-4 pl-4 pb-[56px] resize-none bg-white text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 overflow-y-auto\",\n                                                rows: 5,\n                                                onKeyDown: (e)=>{\n                                                    if (e.key === \"Enter\" && e.shiftKey) {\n                                                        return; // Allow shift+enter for new line\n                                                    } else if (e.key === \"Enter\") {\n                                                        e.preventDefault();\n                                                        handleSubmit(e);\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-0 left-0 right-0 flex items-center justify-between p-3 border-t border-white opacity-100 text-white bg-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: inputMode,\n                                                            onChange: (e)=>setInputMode(e.target.value),\n                                                            className: \"border rounded-full px-3 py-1.5 focus:outline-none focus:ring-1 focus:ring-blue-500 text-slate-950 text-xs tracking-tight leading-7 font-medium bg-slate-200 border-slate-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"nl\",\n                                                                    children: \"自然语言查询\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 369,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"sql\",\n                                                                    children: \"SQL 编辑器\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 370,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            type: \"submit\",\n                                                            size: \"icon\",\n                                                            className: \"bg-blue-600 hover:bg-blue-700 text-white rounded-full h-8 w-8\",\n                                                            disabled: isProcessing || !query.trim(),\n                                                            children: isProcessing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sql_editor__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    value: sqlQuery,\n                                                    onChange: setSqlQuery,\n                                                    placeholder: \"输入 SQL 查询语句...\",\n                                                    height: \"141px\",\n                                                    darkMode: false\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-3 border-t bg-white border-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: inputMode,\n                                                            onChange: (e)=>setInputMode(e.target.value),\n                                                            className: \"border rounded-full px-3 py-1.5 focus:outline-none focus:ring-1 focus:ring-blue-500 text-xs font-medium tracking-tight bg-slate-200 border-slate-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"nl\",\n                                                                    children: \"自然语言查询\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 408,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"sql\",\n                                                                    children: \"SQL 编辑器\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 409,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            type: \"submit\",\n                                                            size: \"icon\",\n                                                            className: \"bg-blue-600 hover:bg-blue-700 text-white rounded-full h-8 w-8\",\n                                                            disabled: isProcessing || !sqlQuery.trim(),\n                                                            children: isProcessing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 423,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"gqcRfK0wLink7NnsKtYLKUU2kFs=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});