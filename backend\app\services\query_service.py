"""
查询执行服务
负责执行SQL查询和结果处理
"""

import logging
import asyncio
import hashlib
import json
from typing import Dict, Any, Optional, List, Tuple, AsyncGenerator
from sqlalchemy.orm import Session
from sqlalchemy import create_engine, text
from datetime import datetime
import pandas as pd
import redis
from asyncio import Semaphore

from app.models.query import QueryHistory, QueryStatus, FavoriteQuery
from app.models.data_source import DataSource
from app.services.data_source_service import DataSourceService
from app.core.security import decrypt_data
from app.core.config import settings
from app.core.exceptions import QueryExecutionError, DataSourceError

logger = logging.getLogger(__name__)

class QueryService:
    """查询服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.data_source_service = DataSourceService(db)
        self.nl2sql_service = NL2SQLService(db)
        # 添加并发控制
        self._query_semaphore = Semaphore(settings.MAX_CONCURRENT_QUERIES)
        self._connection_pool = {}
        self.redis_client = redis.from_url(settings.REDIS_URL) if settings.ENABLE_QUERY_CACHE else None
    
    async def execute_query(
        self,
        sql: str,
        data_source_id: int,
        user_id: int,
        natural_language_query: Optional[str] = None,
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """执行SQL查询"""
        try:
            # 获取数据源
            data_source = self.data_source_service.get_data_source_by_id(data_source_id)
            if not data_source:
                raise DataSourceError("数据源不存在")
            
            # 生成缓存键
            cache_key = None
            if use_cache and settings.ENABLE_QUERY_CACHE:
                cache_key = self._generate_cache_key(sql, data_source_id)
                cached_result = self._get_cached_result(cache_key)
                if cached_result:
                    logger.info(f"使用缓存结果: {cache_key}")
                    return cached_result
            
            # 执行查询
            start_time = datetime.utcnow()
            result = await self._execute_sql(sql, data_source)
            end_time = datetime.utcnow()
            
            execution_time = (end_time - start_time).total_seconds()
            
            # 保存查询历史
            query_history = QueryHistory(
                user_id=user_id,
                natural_language_query=natural_language_query,
                generated_sql=sql,
                executed_sql=sql,
                data_source_id=data_source_id,
                execution_time=execution_time,
                row_count=len(result["data"]) if result["data"] else 0,
                status=QueryStatus.SUCCESS,
                results_cached=bool(cache_key),
                cache_key=cache_key
            )
            
            self.db.add(query_history)
            self.db.commit()
            
            # 缓存结果
            if cache_key and settings.ENABLE_QUERY_CACHE:
                self._cache_result(cache_key, result)
            
            result.update({
                "query_id": query_history.id,
                "execution_time": execution_time,
                "cached": False
            })
            
            logger.info(f"查询执行成功: {query_history.id}")
            return result
            
        except Exception as e:
            # 保存错误记录
            error_history = QueryHistory(
                user_id=user_id,
                natural_language_query=natural_language_query,
                generated_sql=sql,
                executed_sql=sql,
                data_source_id=data_source_id,
                status=QueryStatus.ERROR,
                error_message=str(e)
            )
            
            self.db.add(error_history)
            self.db.commit()
            
            logger.error(f"查询执行失败: {e}")
            raise QueryExecutionError(f"查询执行失败: {str(e)}")
    
    async def _execute_sql(self, sql: str, data_source: DataSource) -> Dict[str, Any]:
        """执行SQL语句"""
        try:
            # 解密密码
            password = decrypt_data(data_source.password_encrypted)
            
            # 构建连接字符串
            connection_url = self.data_source_service._build_connection_url(
                data_source.type,
                data_source.host,
                data_source.port,
                data_source.database_name,
                data_source.username,
                password
            )
            
            # 创建引擎
            engine = create_engine(
                connection_url,
                pool_pre_ping=True,
                pool_recycle=300
            )
            
            # 执行查询
            with engine.connect() as conn:
                # 设置查询超时
                conn = conn.execution_options(
                    autocommit=True,
                    compiled_cache={},
                    isolation_level="READ_COMMITTED"
                )
                
                result = conn.execute(text(sql))
                
                # 获取列名
                columns = list(result.keys()) if result.keys() else []
                
                # 获取数据
                rows = result.fetchall()
                
                # 限制返回行数
                if len(rows) > settings.MAX_QUERY_RESULT_ROWS:
                    rows = rows[:settings.MAX_QUERY_RESULT_ROWS]
                    truncated = True
                else:
                    truncated = False
                
                # 转换为字典列表
                data = []
                for row in rows:
                    row_dict = {}
                    for i, column in enumerate(columns):
                        value = row[i]
                        # 处理特殊数据类型
                        if isinstance(value, datetime):
                            value = value.isoformat()
                        elif hasattr(value, '__str__'):
                            value = str(value)
                        row_dict[column] = value
                    data.append(row_dict)
                
                return {
                    "success": True,
                    "data": data,
                    "columns": columns,
                    "row_count": len(data),
                    "truncated": truncated,
                    "message": f"查询成功，返回 {len(data)} 行数据"
                }
                
        except Exception as e:
            logger.error(f"SQL执行失败: {e}")
            raise QueryExecutionError(f"SQL执行失败: {str(e)}")
    
    def _generate_cache_key(self, sql: str, data_source_id: int) -> str:
        """生成缓存键"""
        content = f"{sql}:{data_source_id}"
        return f"query_cache:{hashlib.md5(content.encode()).hexdigest()}"
    
    def _get_cached_result(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """获取缓存结果"""
        if not self.redis_client:
            return None
        
        try:
            cached_data = self.redis_client.get(cache_key)
            if cached_data:
                result = json.loads(cached_data)
                result["cached"] = True
                return result
        except Exception as e:
            logger.warning(f"获取缓存失败: {e}")
        
        return None
    
    def _cache_result(self, cache_key: str, result: Dict[str, Any]) -> None:
        """缓存查询结果"""
        if not self.redis_client:
            return
        
        try:
            # 移除不需要缓存的字段
            cache_data = result.copy()
            cache_data.pop("query_id", None)
            cache_data.pop("execution_time", None)
            
            self.redis_client.setex(
                cache_key,
                settings.QUERY_CACHE_EXPIRE,
                json.dumps(cache_data, ensure_ascii=False)
            )
        except Exception as e:
            logger.warning(f"缓存结果失败: {e}")
    
    def get_query_history(
        self,
        user_id: int,
        skip: int = 0,
        limit: int = 50,
        data_source_id: Optional[int] = None,
        status: Optional[QueryStatus] = None
    ) -> List[QueryHistory]:
        """获取查询历史"""
        query = self.db.query(QueryHistory).filter(QueryHistory.user_id == user_id)
        
        if data_source_id:
            query = query.filter(QueryHistory.data_source_id == data_source_id)
        
        if status:
            query = query.filter(QueryHistory.status == status)
        
        return query.order_by(QueryHistory.created_at.desc()).offset(skip).limit(limit).all()
    
    def create_favorite_query(
        self,
        user_id: int,
        title: str,
        sql_query: str,
        natural_language_query: Optional[str] = None,
        description: Optional[str] = None,
        tags: Optional[List[str]] = None,
        data_source_id: Optional[int] = None,
        is_public: bool = False
    ) -> FavoriteQuery:
        """创建收藏查询"""
        try:
            favorite = FavoriteQuery(
                user_id=user_id,
                title=title,
                natural_language_query=natural_language_query,
                sql_query=sql_query,
                description=description,
                tags=tags or [],
                data_source_id=data_source_id,
                is_public=is_public
            )
            
            self.db.add(favorite)
            self.db.commit()
            self.db.refresh(favorite)
            
            logger.info(f"收藏查询创建成功: {title}")
            return favorite
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"收藏查询创建失败: {e}")
            raise QueryExecutionError(f"收藏查询创建失败: {str(e)}")
    
    def get_favorite_queries(
        self,
        user_id: int,
        skip: int = 0,
        limit: int = 50,
        search: Optional[str] = None,
        tags: Optional[List[str]] = None,
        include_public: bool = True
    ) -> List[FavoriteQuery]:
        """获取收藏查询列表"""
        query = self.db.query(FavoriteQuery)
        
        # 用户自己的收藏或公开的收藏
        if include_public:
            query = query.filter(
                (FavoriteQuery.user_id == user_id) | 
                (FavoriteQuery.is_public == True)
            )
        else:
            query = query.filter(FavoriteQuery.user_id == user_id)
        
        # 搜索条件
        if search:
            query = query.filter(
                (FavoriteQuery.title.contains(search)) |
                (FavoriteQuery.description.contains(search)) |
                (FavoriteQuery.natural_language_query.contains(search))
            )
        
        # 标签过滤
        if tags:
            for tag in tags:
                query = query.filter(FavoriteQuery.tags.contains(tag))
        
        return query.order_by(FavoriteQuery.created_at.desc()).offset(skip).limit(limit).all()
    
    async def export_query_results(
        self,
        query_id: int,
        format: str = "csv",
        user_id: int = None
    ) -> Dict[str, Any]:
        """导出查询结果"""
        try:
            # 获取查询历史
            query_history = self.db.query(QueryHistory).filter(
                QueryHistory.id == query_id
            ).first()
            
            if not query_history:
                raise QueryExecutionError("查询记录不存在")
            
            # 权限检查
            if user_id and query_history.user_id != user_id:
                raise QueryExecutionError("权限不足")
            
            # 重新执行查询获取完整结果
            result = await self.execute_query(
                query_history.executed_sql,
                query_history.data_source_id,
                query_history.user_id,
                use_cache=False
            )
            
            if not result["success"]:
                raise QueryExecutionError("查询执行失败")
            
            # 转换为DataFrame
            df = pd.DataFrame(result["data"])
            
            # 根据格式导出
            if format.lower() == "csv":
                output = df.to_csv(index=False, encoding='utf-8-sig')
                content_type = "text/csv"
                filename = f"query_result_{query_id}.csv"
            elif format.lower() == "excel":
                import io
                output = io.BytesIO()
                df.to_excel(output, index=False, engine='openpyxl')
                output.seek(0)
                content_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                filename = f"query_result_{query_id}.xlsx"
            elif format.lower() == "json":
                output = df.to_json(orient='records', ensure_ascii=False, indent=2)
                content_type = "application/json"
                filename = f"query_result_{query_id}.json"
            else:
                raise QueryExecutionError(f"不支持的导出格式: {format}")
            
            return {
                "success": True,
                "content": output,
                "content_type": content_type,
                "filename": filename,
                "row_count": len(df)
            }
            
        except Exception as e:
            logger.error(f"查询结果导出失败: {e}")
            raise QueryExecutionError(f"查询结果导出失败: {str(e)}")
    
    async def execute_query_with_streaming(
        self,
        sql: str,
        data_source_id: int,
        user_id: int,
        chunk_size: int = 1000
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """流式执行大数据量查询"""
        async with self._query_semaphore:
            try:
                data_source = self.data_source_service.get_data_source_by_id(data_source_id)
                if not data_source:
                    raise DataSourceError("数据源不存在")
                
                # 流式查询实现
                async for chunk in self._execute_streaming_query(sql, data_source, chunk_size):
                    yield {
                        "chunk_data": chunk,
                        "has_more": True,
                        "chunk_size": len(chunk)
                    }
                
                yield {"chunk_data": [], "has_more": False, "chunk_size": 0}
                
            except Exception as e:
                logger.error(f"流式查询执行失败: {e}")
                raise
