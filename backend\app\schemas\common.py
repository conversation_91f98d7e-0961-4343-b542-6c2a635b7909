"""
通用的Pydantic模式
"""

from typing import Any, Generic, List, Optional, TypeVar, Dict
from pydantic import BaseModel
from app.core.error_codes import ErrorCode

T = TypeVar('T')

class ResponseModel(BaseModel, Generic[T]):
    """通用响应模型"""
    success: bool
    message: str
    data: Optional[T] = None
    error_code: Optional[ErrorCode] = None
    timestamp: Optional[str] = None
    request_id: Optional[str] = None
    
    def __init__(self, **data):
        if 'timestamp' not in data:
            from datetime import datetime
            data['timestamp'] = datetime.utcnow().isoformat()
        super().__init__(**data)

class PaginatedResponse(ResponseModel[List[T]]):
    """分页响应模型"""
    total: int
    page: int
    page_size: int
    total_pages: Optional[int] = None
    has_next: bool = False
    has_prev: bool = False
    
    def __init__(self, **data):
        super().__init__(**data)
        if self.total and self.page_size:
            self.total_pages = (self.total + self.page_size - 1) // self.page_size
            self.has_next = self.page < self.total_pages
            self.has_prev = self.page > 1

class StreamResponse(BaseModel):
    """流式响应模型"""
    chunk_id: str
    data: Any
    has_more: bool
    total_chunks: Optional[int] = None
    progress: Optional[float] = None

