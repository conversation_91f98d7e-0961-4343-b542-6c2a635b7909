import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// API配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1'

// API客户端类
class ApiClient {
  private baseURL: string
  private token: string | null = null

  constructor(baseURL: string) {
    this.baseURL = baseURL
    // 从localStorage获取token
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('access_token')
    }
  }

  setToken(token: string) {
    this.token = token
    if (typeof window !== 'undefined') {
      localStorage.setItem('access_token', token)
    }
  }

  clearToken() {
    this.token = null
    if (typeof window !== 'undefined') {
      localStorage.removeItem('access_token')
    }
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    }

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`
    }

    const config: RequestInit = {
      ...options,
      headers,
    }

    try {
      const response = await fetch(url, config)

      if (!response.ok) {
        if (response.status === 401) {
          // Token过期，清除token
          this.clearToken()
          throw new Error('认证失败，请重新登录')
        }
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('API request failed:', error)
      throw error
    }
  }

  // GET请求
  async get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' })
  }

  // POST请求
  async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  // PUT请求
  async put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  // DELETE请求
  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' })
  }

  // 登录
  async login(username: string, password: string) {
    const formData = new FormData()
    formData.append('username', username)
    formData.append('password', password)

    const response = await fetch(`${this.baseURL}/auth/login`, {
      method: 'POST',
      body: formData,
    })

    if (!response.ok) {
      throw new Error('登录失败')
    }

    const data = await response.json()
    this.setToken(data.access_token)
    return data
  }

  // 注册
  async register(userData: {
    username: string
    email: string
    password: string
    full_name?: string
  }) {
    return this.post('/auth/register', userData)
  }

  // 自然语言转SQL并执行
  async nlToSqlAndExecute(query: string, dataSourceId: number = 1) {
    const response = await this.post('/query/nl2sql-and-execute', {
      natural_query: query,
      data_source_id: dataSourceId,
    })

    // 处理后端返回的嵌套数据结构
    if (response.success && response.data) {
      return {
        success: true,
        data: response.data.data,
        columns: response.data.columns,
        sql: response.data.generated_sql,
        message: response.message
      }
    }

    return response
  }

  // 执行SQL查询
  async executeQuery(sql: string, dataSourceId: number = 1) {
    const response = await this.post('/query/execute', {
      sql,
      data_source_id: dataSourceId,
    })

    // 处理后端返回的嵌套数据结构
    if (response.success && response.data) {
      return {
        success: true,
        data: response.data.data || response.data,
        columns: response.data.columns,
        message: response.message
      }
    }

    return response
  }

  // 获取数据源列表
  async getDataSources() {
    return this.get('/data-sources/')
  }

  // 获取查询历史
  async getQueryHistory() {
    return this.get('/query/history')
  }

  // 获取收藏查询
  async getFavoriteQueries() {
    return this.get('/favorites/')
  }

  // 获取系统健康状态
  async getSystemHealth() {
    return this.get('/system/health')
  }
}

// 创建全局API客户端实例
export const apiClient = new ApiClient(API_BASE_URL)
