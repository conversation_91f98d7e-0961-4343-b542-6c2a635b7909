"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/table.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-no-axes-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-line.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-pie.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-down.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart2,ChevronLeft,ChevronRight,Database,Download,LineChart,PieChart,Search,Send,Star,TableIcon,ThumbsDown,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./components/ui/tooltip.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_metadata_explorer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/metadata-explorer */ \"(app-pages-browser)/./components/metadata-explorer.tsx\");\n/* harmony import */ var _components_query_history__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/query-history */ \"(app-pages-browser)/./components/query-history.tsx\");\n/* harmony import */ var _components_results_display__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/results-display */ \"(app-pages-browser)/./components/results-display.tsx\");\n/* harmony import */ var _components_favorite_queries__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/favorite-queries */ \"(app-pages-browser)/./components/favorite-queries.tsx\");\n/* harmony import */ var _components_sql_editor__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/sql-editor */ \"(app-pages-browser)/./components/sql-editor.tsx\");\n/* harmony import */ var _components_example_queries__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/example-queries */ \"(app-pages-browser)/./components/example-queries.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n // Corrected import path\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"metadata\");\n    const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [sqlQuery, setSqlQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"table\");\n    const [chartType, setChartType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"bar\");\n    const [inputMode, setInputMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"nl\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [apiConnected, setApiConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (inputMode === \"nl\" && !query.trim()) return;\n        if (inputMode === \"sql\" && !sqlQuery.trim()) return;\n        setIsProcessing(true);\n        setError(\"\");\n        try {\n            let response;\n            if (inputMode === \"nl\") {\n                // 自然语言转SQL并执行\n                response = await _lib_utils__WEBPACK_IMPORTED_MODULE_12__.apiClient.nlToSqlAndExecute(query);\n                if (response.success) {\n                    // 设置生成的SQL\n                    if (response.sql) {\n                        setSqlQuery(response.sql);\n                    }\n                    // 设置查询结果\n                    if (response.data) {\n                        setResults({\n                            columns: response.columns || Object.keys(response.data[0] || {}),\n                            data: response.data\n                        });\n                    }\n                    // 切换到SQL编辑器模式\n                    setInputMode(\"sql\");\n                } else {\n                    setError(response.message || '查询失败');\n                }\n            } else {\n                // 执行SQL查询\n                response = await _lib_utils__WEBPACK_IMPORTED_MODULE_12__.apiClient.executeQuery(sqlQuery);\n                if (response.success && response.data) {\n                    setResults({\n                        columns: response.columns || Object.keys(response.data[0] || {}),\n                        data: response.data\n                    });\n                } else {\n                    setError(response.message || 'SQL执行失败');\n                }\n            }\n        } catch (error) {\n            console.error('API调用失败:', error);\n            setError(\"连接后端失败: \".concat(error.message));\n            // 如果API调用失败，回退到模拟数据\n            const generatedSql = inputMode === \"nl\" ? \"SELECT product_name, SUM(sales_amount) as total_sales FROM sales_data GROUP BY product_name ORDER BY total_sales DESC LIMIT 10;\" : sqlQuery;\n            setSqlQuery(generatedSql);\n            setResults({\n                columns: [\n                    \"product_name\",\n                    \"total_sales\"\n                ],\n                data: [\n                    {\n                        product_name: \"Product A\",\n                        total_sales: 12500\n                    },\n                    {\n                        product_name: \"Product B\",\n                        total_sales: 9800\n                    },\n                    {\n                        product_name: \"Product C\",\n                        total_sales: 7600\n                    },\n                    {\n                        product_name: \"Product D\",\n                        total_sales: 6200\n                    },\n                    {\n                        product_name: \"Product E\",\n                        total_sales: 5100\n                    }\n                ]\n            });\n            if (inputMode === \"nl\") {\n                setInputMode(\"sql\");\n            }\n        } finally{\n            setIsProcessing(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-screen bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-1 overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"relative flex flex-col border-r transition-all duration-300 bg-gray-50 opacity-100 border-slate-50\", sidebarOpen ? \"w-72\" : \"w-0\"),\n                    children: [\n                        sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative mx-2 mt-4 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                    type: \"text\",\n                                    placeholder: \"搜索库表、历史查询...\",\n                                    className: \"w-full pl-9 pr-3 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-1 focus:ring-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, this),\n                        sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                            value: activeTab,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsList, {\n                                    className: \"grid grid-cols-3 mx-2 mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                            value: \"metadata\",\n                                            onClick: ()=>setActiveTab(\"metadata\"),\n                                            children: \"元数据\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                            value: \"favorites\",\n                                            onClick: ()=>setActiveTab(\"favorites\"),\n                                            children: \"收藏夹\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                            value: \"history\",\n                                            onClick: ()=>setActiveTab(\"history\"),\n                                            children: \"历史查询\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                    value: \"metadata\",\n                                    className: \"flex-1 p-0 m-0 pb-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                                        className: \"h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_metadata_explorer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                    value: \"favorites\",\n                                    className: \"flex-1 p-0 m-0 pb-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                                        className: \"h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_favorite_queries__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                    value: \"history\",\n                                    className: \"flex-1 p-0 m-0 pb-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                                        className: \"h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_query_history__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 13\n                        }, this),\n                        sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-0 left-0 right-0 p-2 bg-gray-50 border-t border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    onClick: ()=>setSidebarOpen(false),\n                                    className: \"h-8 text-gray-600 hover:text-gray-900 hover:bg-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex flex-col h-full overflow-hidden\",\n                    children: [\n                        !sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"absolute bottom-2 left-0 z-10 p-1 bg-white border border-gray-200 rounded-r-md\",\n                            onClick: ()=>setSidebarOpen(!sidebarOpen),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                size: 18\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-auto p-6\",\n                            children: [\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 15\n                                }, this),\n                                !results ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full flex flex-col items-center justify-center text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            size: 48,\n                                            className: \"mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-medium mb-2\",\n                                            children: \"欢迎使用 NL2SQL 数据智能分析系统\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-center max-w-md mb-8\",\n                                            children: [\n                                                \"输入自然语言查询，系统将自动转换为 SQL 并从数据仓库中检索结果。\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"您可以在左侧浏览数据仓库的元数据结构。\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_example_queries__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            onSelectQuery: (q)=>{\n                                                setQuery(q);\n                                                setInputMode(\"nl\");\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg border border-gray-200 overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium\",\n                                                            children: \"查询结果\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                    variant: \"outline\",\n                                                                                    size: \"icon\",\n                                                                                    onClick: ()=>setViewMode(\"table\"),\n                                                                                    className: viewMode === \"table\" ? \"bg-gray-100\" : \"\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                        size: 16\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 241,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 235,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 234,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                children: \"表格视图\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 244,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 233,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 232,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                    variant: \"outline\",\n                                                                                    size: \"icon\",\n                                                                                    onClick: ()=>{\n                                                                                        setViewMode(\"chart\");\n                                                                                        setChartType(\"bar\");\n                                                                                    },\n                                                                                    className: viewMode === \"chart\" && chartType === \"bar\" ? \"bg-gray-100\" : \"\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        size: 16\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 260,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 251,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 250,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                children: \"柱状图\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 263,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 249,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 248,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                    variant: \"outline\",\n                                                                                    size: \"icon\",\n                                                                                    onClick: ()=>{\n                                                                                        setViewMode(\"chart\");\n                                                                                        setChartType(\"line\");\n                                                                                    },\n                                                                                    className: viewMode === \"chart\" && chartType === \"line\" ? \"bg-gray-100\" : \"\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                        size: 16\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 279,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 270,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 269,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                children: \"折线图\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 282,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 268,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 267,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                    variant: \"outline\",\n                                                                                    size: \"icon\",\n                                                                                    onClick: ()=>{\n                                                                                        setViewMode(\"chart\");\n                                                                                        setChartType(\"pie\");\n                                                                                    },\n                                                                                    className: viewMode === \"chart\" && chartType === \"pie\" ? \"bg-gray-100\" : \"\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                        size: 16\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 298,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 289,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 288,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                children: \"饼图\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 301,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 287,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 286,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                    variant: \"outline\",\n                                                                                    size: \"icon\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        size: 16\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 309,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 308,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 307,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                children: \"下载结果\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 312,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 306,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 305,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_results_display__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    results: results,\n                                                    viewMode: viewMode,\n                                                    chartType: chartType\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"收藏查询\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"结果准确吗��\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"icon\",\n                                                            className: \"h-8 w-8 bg-transparent\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"icon\",\n                                                            className: \"h-8 w-8 bg-transparent\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t p-4 bg-white border-white\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"flex flex-col\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative bg-white rounded-lg overflow-hidden border border-gray-300\",\n                                    children: inputMode === \"nl\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: query,\n                                                onChange: (e)=>setQuery(e.target.value),\n                                                placeholder: \"有问题，尽管问，shift+enter换行\",\n                                                className: \"w-full pt-4 pr-4 pl-4 pb-[56px] resize-none bg-white text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 overflow-y-auto\",\n                                                rows: 5,\n                                                onKeyDown: (e)=>{\n                                                    if (e.key === \"Enter\" && e.shiftKey) {\n                                                        return; // Allow shift+enter for new line\n                                                    } else if (e.key === \"Enter\") {\n                                                        e.preventDefault();\n                                                        handleSubmit(e);\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-0 left-0 right-0 flex items-center justify-between p-3 border-t border-white opacity-100 text-white bg-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: inputMode,\n                                                            onChange: (e)=>setInputMode(e.target.value),\n                                                            className: \"border rounded-full px-3 py-1.5 focus:outline-none focus:ring-1 focus:ring-blue-500 text-slate-950 text-xs tracking-tight leading-7 font-medium bg-slate-200 border-slate-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"nl\",\n                                                                    children: \"自然语言查询\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 370,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"sql\",\n                                                                    children: \"SQL 编辑器\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            type: \"submit\",\n                                                            size: \"icon\",\n                                                            className: \"bg-blue-600 hover:bg-blue-700 text-white rounded-full h-8 w-8\",\n                                                            disabled: isProcessing || !query.trim(),\n                                                            children: isProcessing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sql_editor__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    value: sqlQuery,\n                                                    onChange: setSqlQuery,\n                                                    placeholder: \"输入 SQL 查询语句...\",\n                                                    height: \"141px\",\n                                                    darkMode: false\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                    lineNumber: 394,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-3 border-t bg-white border-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: inputMode,\n                                                            onChange: (e)=>setInputMode(e.target.value),\n                                                            className: \"border rounded-full px-3 py-1.5 focus:outline-none focus:ring-1 focus:ring-blue-500 text-xs font-medium tracking-tight bg-slate-200 border-slate-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"nl\",\n                                                                    children: \"自然语言查询\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 409,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"sql\",\n                                                                    children: \"SQL 编辑器\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 410,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            type: \"submit\",\n                                                            size: \"icon\",\n                                                            className: \"bg-blue-600 hover:bg-blue-700 text-white rounded-full h-8 w-8\",\n                                                            disabled: isProcessing || !sqlQuery.trim(),\n                                                            children: isProcessing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart2_ChevronLeft_ChevronRight_Database_Download_LineChart_PieChart_Search_Send_Star_TableIcon_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                            lineNumber: 343,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\app\\\\page.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"pTf2Hzbhj+zbFlCIWp/BIXe74pI=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});