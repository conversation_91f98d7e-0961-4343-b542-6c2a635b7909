"use client"

import type React from "react"
import { useState } from "react"
import {
  Send,
  Download,
  Star,
  ChevronRight,
  ThumbsUp,
  ThumbsDown,
  BarChart2,
  TableIcon,
  PieChart,
  LineChart,
  Database,
  Search,
  ChevronLeft,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>ltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import MetadataExplorer from "@/components/metadata-explorer"
import QueryHistory from "@/components/query-history"
import ResultsDisplay from "@/components/results-display" // Corrected import path
import FavoriteQueries from "@/components/favorite-queries"
import SqlEditor from "@/components/sql-editor"
import ExampleQueries from "@/components/example-queries"
import { cn } from "@/lib/utils"
import { Input } from "@/components/ui/input"

export default function Home() {
  const [sidebarO<PERSON>, setSidebarOpen] = useState(true)
  const [activeTab, setActiveTab] = useState<"metadata" | "favorites" | "history">("metadata")
  const [query, setQuery] = useState("")
  const [sqlQuery, setSqlQuery] = useState("")
  const [isProcessing, setIsProcessing] = useState(false)
  const [results, setResults] = useState<any>(null)
  const [viewMode, setViewMode] = useState<"table" | "chart">("table")
  const [chartType, setChartType] = useState<"bar" | "line" | "pie">("bar")
  const [inputMode, setInputMode] = useState<"nl" | "sql">("nl")

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (inputMode === "nl" && !query.trim()) return
    if (inputMode === "sql" && !sqlQuery.trim()) return

    setIsProcessing(true)

    // Simulate processing
    setTimeout(() => {
      // Mock response
      const generatedSql =
        inputMode === "nl"
          ? "SELECT product_name, SUM(sales_amount) as total_sales FROM sales_data GROUP BY product_name ORDER BY total_sales DESC LIMIT 10;"
          : sqlQuery

      setSqlQuery(generatedSql)
      setResults({
        columns: ["product_name", "total_sales"],
        data: [
          { product_name: "Product A", total_sales: 12500 },
          { product_name: "Product B", total_sales: 9800 },
          { product_name: "Product C", total_sales: 7600 },
          { product_name: "Product D", total_sales: 6200 },
          { product_name: "Product E", total_sales: 5100 },
          { product_name: "Product F", total_sales: 4300 },
          { product_name: "Product G", total_sales: 3800 },
          { product_name: "Product H", total_sales: 3200 },
          { product_name: "Product I", total_sales: 2700 },
          { product_name: "Product J", total_sales: 2100 },
        ],
      })
      setIsProcessing(false)

      // Switch to SQL editor mode if the query was natural language
      if (inputMode === "nl") {
        setInputMode("sql")
      }
    }, 1500)
  }

  return (
    <div className="flex flex-col h-screen bg-white">
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar */}
        <div
          className={cn(
            "relative flex flex-col border-r transition-all duration-300 bg-gray-50 opacity-100 border-slate-50",
            sidebarOpen ? "w-72" : "w-0",
          )}
        >
          {sidebarOpen && (
            <div className="relative mx-2 mt-4 mb-2">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500" />
              <Input
                type="text"
                placeholder="搜索库表、历史查询..."
                className="w-full pl-9 pr-3 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
            </div>
          )}
          {sidebarOpen && (
            <Tabs value={activeTab}>
              <TabsList className="grid grid-cols-3 mx-2 mt-4">
                <TabsTrigger value="metadata" onClick={() => setActiveTab("metadata")}>
                  元数据
                </TabsTrigger>
                <TabsTrigger value="favorites" onClick={() => setActiveTab("favorites")}>
                  收藏夹
                </TabsTrigger>
                <TabsTrigger value="history" onClick={() => setActiveTab("history")}>
                  历史查询
                </TabsTrigger>
              </TabsList>
              <TabsContent value="metadata" className="flex-1 p-0 m-0 pb-12">
                <ScrollArea className="h-full">
                  <MetadataExplorer />
                </ScrollArea>
              </TabsContent>
              <TabsContent value="favorites" className="flex-1 p-0 m-0 pb-12">
                <ScrollArea className="h-full">
                  <FavoriteQueries />
                </ScrollArea>
              </TabsContent>
              <TabsContent value="history" className="flex-1 p-0 m-0 pb-12">
                <ScrollArea className="h-full">
                  <QueryHistory />
                </ScrollArea>
              </TabsContent>
            </Tabs>
          )}
          {sidebarOpen && (
            <div className="absolute bottom-0 left-0 right-0 p-2 bg-gray-50 border-t border-gray-200">
              <div className="flex justify-end">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSidebarOpen(false)}
                  className="h-8 text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col h-full overflow-hidden">
          {/* Toggle Sidebar Button (when sidebar is closed) */}
          {!sidebarOpen && (
            <button
              className="absolute bottom-2 left-0 z-10 p-1 bg-white border border-gray-200 rounded-r-md"
              onClick={() => setSidebarOpen(!sidebarOpen)}
            >
              <ChevronRight size={18} />
            </button>
          )}

          {/* Results Area */}
          <div className="flex-1 overflow-auto p-6">
            {!results ? (
              <div className="h-full flex flex-col items-center justify-center text-gray-500">
                <Database size={48} className="mb-4" />
                <h3 className="text-xl font-medium mb-2">欢迎使用 NL2SQL 数据智能分析系统</h3>
                <p className="text-center max-w-md mb-8">
                  输入自然语言查询，系统将自动转换为 SQL 并从数据仓库中检索结果。
                  <br />
                  您可以在左侧浏览数据仓库的元数据结构。
                </p>

                <ExampleQueries
                  onSelectQuery={(q) => {
                    setQuery(q)
                    setInputMode("nl")
                  }}
                />
              </div>
            ) : (
              <div className="space-y-4">
                <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                  <div className="flex items-center justify-between p-4 border-b border-gray-200">
                    <h3 className="font-medium">查询结果</h3>
                    <div className="flex items-center space-x-2">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => setViewMode("table")}
                              className={viewMode === "table" ? "bg-gray-100" : ""}
                            >
                              <TableIcon size={16} />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>表格视图</TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => {
                                setViewMode("chart")
                                setChartType("bar")
                              }}
                              className={viewMode === "chart" && chartType === "bar" ? "bg-gray-100" : ""}
                            >
                              <BarChart2 size={16} />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>柱状图</TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => {
                                setViewMode("chart")
                                setChartType("line")
                              }}
                              className={viewMode === "chart" && chartType === "line" ? "bg-gray-100" : ""}
                            >
                              <LineChart size={16} />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>折线图</TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => {
                                setViewMode("chart")
                                setChartType("pie")
                              }}
                              className={viewMode === "chart" && chartType === "pie" ? "bg-gray-100" : ""}
                            >
                              <PieChart size={16} />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>饼图</TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button variant="outline" size="icon">
                              <Download size={16} />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>下载结果</TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </div>

                  <ResultsDisplay results={results} viewMode={viewMode} chartType={chartType} />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm">
                      <Star className="h-4 w-4 mr-1" />
                      收藏查询
                    </Button>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-500">结果准确吗��</span>
                    <Button variant="outline" size="icon" className="h-8 w-8 bg-transparent">
                      <ThumbsUp className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="icon" className="h-8 w-8 bg-transparent">
                      <ThumbsDown className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Query Input Area */}
          <div className="border-t p-4 bg-white border-white">
            <form onSubmit={handleSubmit} className="flex flex-col">
              <div className="relative bg-white rounded-lg overflow-hidden border border-gray-300">
                {inputMode === "nl" ? (
                  <>
                    <textarea
                      value={query}
                      onChange={(e) => setQuery(e.target.value)}
                      placeholder="有问题，尽管问，shift+enter换行"
                      className="w-full pt-4 pr-4 pl-4 pb-[56px] resize-none bg-white text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 overflow-y-auto"
                      rows={5}
                      onKeyDown={(e) => {
                        if (e.key === "Enter" && e.shiftKey) {
                          return // Allow shift+enter for new line
                        } else if (e.key === "Enter") {
                          e.preventDefault()
                          handleSubmit(e)
                        }
                      }}
                    />
                    <div className="absolute bottom-0 left-0 right-0 flex items-center justify-between p-3 border-t border-white opacity-100 text-white bg-white">
                      <div className="flex items-center space-x-3">
                        <select
                          value={inputMode}
                          onChange={(e) => setInputMode(e.target.value as "nl" | "sql")}
                          className="border rounded-full px-3 py-1.5 focus:outline-none focus:ring-1 focus:ring-blue-500 text-slate-950 text-xs tracking-tight leading-7 font-medium bg-slate-200 border-slate-300"
                        >
                          <option value="nl">自然语言查询</option>
                          <option value="sql">SQL 编辑器</option>
                        </select>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Button
                          type="submit"
                          size="icon"
                          className="bg-blue-600 hover:bg-blue-700 text-white rounded-full h-8 w-8"
                          disabled={isProcessing || !query.trim()}
                        >
                          {isProcessing ? (
                            <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          ) : (
                            <Send className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="bg-white">
                      <SqlEditor
                        value={sqlQuery}
                        onChange={setSqlQuery}
                        placeholder="输入 SQL 查询语句..."
                        height="141px"
                        darkMode={false}
                      />
                    </div>
                    <div className="flex items-center justify-between p-3 border-t bg-white border-white">
                      <div className="flex items-center space-x-3">
                        <select
                          value={inputMode}
                          onChange={(e) => setInputMode(e.target.value as "nl" | "sql")}
                          className="border rounded-full px-3 py-1.5 focus:outline-none focus:ring-1 focus:ring-blue-500 text-xs font-medium tracking-tight bg-slate-200 border-slate-300"
                        >
                          <option value="nl">自然语言查询</option>
                          <option value="sql">SQL 编辑器</option>
                        </select>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Button
                          type="submit"
                          size="icon"
                          className="bg-blue-600 hover:bg-blue-700 text-white rounded-full h-8 w-8"
                          disabled={isProcessing || !sqlQuery.trim()}
                        >
                          {isProcessing ? (
                            <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          ) : (
                            <Send className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  )
}
